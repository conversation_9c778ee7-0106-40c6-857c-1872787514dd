#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script per estrarre il contenuto XML dal template DOCX
"""
import zipfile
import xml.etree.ElementTree as ET
from xml.dom import minidom
import os

def extract_docx_content(docx_path):
    """Estrae il contenuto XML principale dal file DOCX"""
    if not os.path.exists(docx_path):
        print(f"File non trovato: {docx_path}")
        return None
    
    try:
        with zipfile.ZipFile(docx_path, 'r') as docx:
            # Estrai il documento principale
            document_xml = docx.read('word/document.xml')
            
            # Parse e formatta il XML
            root = ET.fromstring(document_xml)
            
            # Converti in stringa formattata
            rough_string = ET.tostring(root, 'unicode')
            reparsed = minidom.parseString(rough_string)
            
            return reparsed.toprettyxml(indent="  ")
    
    except Exception as e:
        print(f"Errore nell'estrazione: {e}")
        return None

def find_docx_templates():
    """Trova i template DOCX nel sistema"""
    possible_paths = [
        'common/templates/publius/persone/religiosi_annuario_per_aree.docx',
        'common/templates/publius/persone/religiosi_annuario_componenti_case.docx',
        'templates/publius/persone/religiosi_annuario_per_aree.docx',
        'templates/publius/persone/religiosi_annuario_componenti_case.docx'
    ]
    
    found_templates = []
    for path in possible_paths:
        if os.path.exists(path):
            found_templates.append(path)
            print(f"✓ Trovato: {path}")
        else:
            print(f"✗ Non trovato: {path}")
    
    return found_templates

if __name__ == "__main__":
    print("=== Ricerca Template DOCX ===")
    templates = find_docx_templates()
    
    if templates:
        print(f"\n=== Estrazione Contenuto ===")
        for template_path in templates:
            print(f"\n--- {template_path} ---")
            content = extract_docx_content(template_path)
            if content:
                # Salva il contenuto in un file
                output_file = f"{os.path.basename(template_path)}_content.xml"
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"Contenuto salvato in: {output_file}")
                
                # Mostra un estratto
                lines = content.split('\n')
                print("Prime 20 righe:")
                for i, line in enumerate(lines[:20]):
                    print(f"{i+1:2d}: {line}")
                if len(lines) > 20:
                    print(f"... (altre {len(lines)-20} righe)")
            else:
                print("Impossibile estrarre il contenuto")
    else:
        print("\nNessun template DOCX trovato!")
