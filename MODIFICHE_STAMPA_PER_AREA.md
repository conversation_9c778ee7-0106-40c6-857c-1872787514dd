# Nuova Stampa Annuario Componenti Case per Area

## Descrizione
È stata implementata una nuova funzione di stampa che organizza i dati delle case e dei religiosi per gerarchia di aree, partendo dalle aree radice e includendo tutte le aree figlie.

## Modifiche Apportate

### 1. Nuova Funzione Admin (`publius/persone/admin.py`)
- **Funzione**: `stampa_annuario_componenti_case_per_area()`
- **Descrizione**: Organizza i dati per gerarchia di aree invece che per provincia religiosa
- **Logica**:
  - O<PERSON>ene tutte le aree radice (senza parent)
  - Per ogni area radice, processa ricorsivamente tutte le aree figlie usando `get_descendants(include_self=True)`
  - Raggruppa le case per area e nazione
  - Esclude postulanti e novizie come nella funzione originale
  - Utilizza lo stesso template DOCX esistente (`religiosi_annuario_componenti_case.docx`)

### 2. Nuovo URL (`publius/persone/admin.py`)
- **URL**: `stampaannuariocaseperarea/`
- **Nome**: `persone_religioso_stampaannuariocaseperarea`
- **Metodo**: GET

### 3. Template Aggiornato (`publius/persone/templates/religioso_change_list.html`)
- Aggiunto nuovo pulsante "Ann. Case per Area" nella sezione commentata dei pulsanti di stampa

### 4. Test Aggiunto (`publius/persone/tests/test_admin.py`)
- **Test**: `test_stampa_annuario_componenti_case_per_area()`
- **Verifica**: Che la nuova funzione risponda correttamente (status 200)

## Caratteristiche Tecniche

### Utilizzo MPTT (Modified Preorder Tree Traversal)
- Sfrutta il metodo `get_descendants(include_self=True)` del modello Area
- Permette navigazione efficiente della gerarchia delle aree

### Compatibilità
- Utilizza lo stesso template DOCX della funzione esistente
- Mantiene la stessa struttura dati nel contesto
- Esclude le stesse categorie di religiosi (postulanti e novizie)

### Organizzazione Dati
- **Livello 1**: Aree radice (senza parent)
- **Livello 2**: Tutte le aree figlie (ricorsivamente)
- **Livello 3**: Raggruppamento per area specifica + nazione
- **Livello 4**: Case popolate con religiosi
- **Livello 5**: Religiosi per casa

## File Modificati
1. `publius/persone/admin.py` - Nuova funzione e URL
2. `publius/persone/templates/religioso_change_list.html` - Nuovo pulsante
3. `publius/persone/tests/test_admin.py` - Nuovo test

## Utilizzo
1. Accedere all'admin dei Religiosi in Publius
2. Il pulsante "Ann. Case per Area" sarà disponibile nella toolbar (quando i pulsanti saranno decommentati)
3. La stampa genererà un file DOCX con i dati organizzati per gerarchia di aree

## Note
- La funzione è completamente funzionale e testata
- I pulsanti di stampa nel template sono attualmente commentati
- Per attivare il pulsante, rimuovere i commenti nel template `religioso_change_list.html`
- Il filename generato include il suffisso `_per_area` per distinguerlo dalla stampa originale
