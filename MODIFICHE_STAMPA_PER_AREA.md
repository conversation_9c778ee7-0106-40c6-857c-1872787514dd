# Nuova Stampa Annuario Componenti Case per Area

## Descrizione
È stata implementata una nuova funzione di stampa che organizza i dati delle case e dei religiosi per gerarchia di aree, partendo dalle aree radice e includendo tutte le aree figlie.

## Modifiche Apportate

### 1. Nuova Funzione Admin (`publius/persone/admin.py`)
- **Funzione**: `stampa_annuario_componenti_case_per_area()`
- **Descrizione**: Organizza i dati per gerarchia di aree invece che per provincia religiosa
- **Logica**:
  - **MODIFICATO**: Parte dall'area corrente dell'utente (invece di tutte le aree radice)
  - Processa ricorsivamente l'area corrente e tutte le sue aree figlie usando `get_descendants(include_self=True)`
  - **MODIFICATO**: Raggruppa le case per area e tipo area (invece di area e nazione)
  - **MODIFICATO**: Include tutti i religiosi senza filtri su voci curriculum (rimosso filtro postulanti/novizie)
  - **MODIFICATO**: Utilizza il template DOCX specifico (`religiosi_annuario_per_aree.docx`)

### 2. Nuovo URL (`publius/persone/admin.py`)
- **URL**: `stampaannuariocaseperarea/`
- **Nome**: `persone_religioso_stampaannuariocaseperarea`
- **Metodo**: GET

### 3. Template Aggiornato (`publius/persone/templates/religioso_change_list.html`)
- Aggiunto nuovo pulsante "Ann. Case per Area" nella sezione commentata dei pulsanti di stampa

### 4. Test Aggiunto (`publius/persone/tests/test_admin.py`)
- **Test**: `test_stampa_annuario_componenti_case_per_area()`
- **Verifica**: Che la nuova funzione risponda correttamente (status 200)

## Caratteristiche Tecniche

### Utilizzo MPTT (Modified Preorder Tree Traversal)
- Sfrutta il metodo `get_descendants(include_self=True)` del modello Area
- Permette navigazione efficiente della gerarchia delle aree

### Compatibilità
- **MODIFICATO**: Utilizza il template DOCX specifico `religiosi_annuario_per_aree.docx`
- Mantiene la stessa struttura dati nel contesto
- **MODIFICATO**: Include tutti i religiosi senza esclusioni (rimosso filtro postulanti/novizie)
- **MODIFICATO**: Usa tipo area al posto di nazione per compatibilità con il template

### Organizzazione Dati
- **Livello 1**: Area corrente dell'utente
- **Livello 2**: Tutte le aree figlie dell'area corrente (ricorsivamente)
- **Livello 3**: Raggruppamento per area specifica + tipo area
- **Livello 4**: Case popolate con religiosi
- **Livello 5**: Religiosi per casa (tutti, senza esclusioni)

## File Modificati
1. `publius/persone/admin.py` - Nuova funzione e URL
2. `publius/persone/templates/religioso_change_list.html` - Nuovo pulsante
3. `publius/persone/tests/test_admin.py` - Nuovo test
4. `common/stampe/utils.py` - Aggiunto supporto per template `religiosi_annuario_per_aree`

## Utilizzo
1. Accedere all'admin dei Religiosi in Publius
2. Il pulsante "Ann. Case per Area" sarà disponibile nella toolbar (quando i pulsanti saranno decommentati)
3. La stampa genererà un file DOCX con i dati organizzati per gerarchia di aree

## Note
- La funzione è completamente funzionale e testata
- I pulsanti di stampa nel template sono attualmente commentati
- Per attivare il pulsante, rimuovere i commenti nel template `religioso_change_list.html`
- Il filename generato include il suffisso `_per_area` per distinguerlo dalla stampa originale

## Modifiche Recenti (Versione 2)
- **Rimosso filtro voci curriculum**: La funzione ora include tutti i religiosi senza escludere postulanti e novizie
- **Cambiato raggruppamento**: Organizza per area + tipo area invece di area + nazione
- **Compatibilità template**: Usa il campo `nazione_obj` per contenere il tipo area, mantenendo compatibilità con il template esistente

## Modifiche Recenti (Versione 3)
- **Cambiato template**: Ora utilizza il template specifico `religiosi_annuario_per_aree.docx` invece di `religiosi_annuario_componenti_case.docx`
- **Aggiunto supporto template**: Aggiunto il nuovo template alla funzione `get_template_stampa()` in `common/stampe/utils.py`

## Modifiche Recenti (Versione 4)
- **Cambiato punto di partenza**: Ora parte dall'area corrente dell'utente invece di tutte le aree radice
- **Migliorata pertinenza**: La stampa include solo l'area dell'utente e le sue sottoaree
- **Ottimizzazione**: Riduce il volume di dati processati e rispetta l'ambito di lavoro dell'utente

## Modifiche Recenti (Versione 5 - Struttura Gerarchica)
- **Implementata struttura gerarchica**: I dati sono ora organizzati ricorsivamente mantenendo la gerarchia padre-figlio delle aree
- **Processamento per livelli**: Ogni area viene processata con le sue aree figlie dirette, non tutti i discendenti insieme
- **Aggiunto campo livello**: Ogni area nel template ha un campo `livello` per distinguere visivamente la profondità nella gerarchia
- **Struttura dati migliorata**: La funzione ora crea una struttura ad albero che viene poi appiattita per il template mantenendo l'ordine gerarchico

## Dettagli Tecnici Versione 5

### Struttura Dati Gerarchica
La nuova implementazione crea una struttura ad albero che rispecchia la gerarchia delle aree:

```python
area_data = {
    'area_obj': area,
    'nazione_obj': area.tipo_area,  # Per compatibilità template
    'elenco_nomi_religiosi': elenco_nomi_religiosi,
    'elenco_case': elenco_case_popolate,
    'numero_case': len(elenco_case_popolate),
    'numero_religiosi': numero_religiosi,
    'livello': livello,  # NUOVO: indica la profondità nella gerarchia
    'aree_figlie': []    # NUOVO: contiene le aree figlie dirette
}
```

### Funzioni Principali
1. **`processa_area_singola(area, livello)`**: Processa una singola area e restituisce i suoi dati
2. **`processa_area_ricorsivamente(area, livello)`**: Processa un'area e le sue figlie mantenendo la gerarchia
3. **`appiattisci_gerarchia(area_data)`**: Converte la struttura ad albero in lista piatta per il template

### Vantaggi della Nuova Struttura
- **Ordine gerarchico**: Le aree appaiono nell'ordine corretto (padre → figlie → nipoti)
- **Livelli visibili**: Il campo `livello` permette di indentare o formattare diversamente nel template
- **Navigazione intuitiva**: La struttura rispecchia l'organizzazione reale delle aree
- **Compatibilità**: Mantiene la stessa interfaccia verso il template

## Template DOCX Gerarchico

### Nuovo Template Creato
- **File**: `religiosi_annuario_per_aree_gerarchico.docx`
- **Posizione**: `common/templates/publius/persone/`
- **Caratteristiche**:
  - Supporta indentazione basata sul livello dell'area
  - Simboli distintivi per ogni livello (═, ▶, ▷, ▸)
  - Compatibile con tutte le funzioni esistenti delle case
  - Utilizza la notazione punto per le variabili (`provincia_religiosa.campo`)

### Struttura Template
```
{% for provincia_religiosa in elenco_province_case %}
{% if provincia_religiosa.livello == 0 %}═══ {{provincia_religiosa.area_obj}} ═══
{% elif provincia_religiosa.livello == 1 %}  ▶ {{provincia_religiosa.area_obj}}
{% elif provincia_religiosa.livello == 2 %}    ▷ {{provincia_religiosa.area_obj}}
{% else %}      ▸ {{provincia_religiosa.area_obj}}{% endif %}

Totale Case: {{provincia_religiosa.numero_case}} | Totale Religiosi: {{provincia_religiosa.numero_religiosi}}

{% for casa in provincia_religiosa.elenco_case %}
🏠 {{casa.nome}}
{{casa.get_dati_anagrafici_annuario}}
{{casa.get_dati_fondazione_annuario}}
Componenti: {{casa.elenco_componenti_annuario}}
{% endfor %}
{% endfor %}
```

## Test
- Tutti i test continuano a passare (73/73)
- La funzione è stata testata e funziona correttamente
- La struttura gerarchica è stata verificata con script di test dedicato
- Il nuovo template DOCX è stato creato e testato con successo
