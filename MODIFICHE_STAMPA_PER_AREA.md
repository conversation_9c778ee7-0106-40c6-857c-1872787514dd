# Nuova Stampa Annuario Componenti Case per Area

## Descrizione
È stata implementata una nuova funzione di stampa che organizza i dati delle case e dei religiosi per gerarchia di aree, partendo dalle aree radice e includendo tutte le aree figlie.

## Modifiche Apportate

### 1. Nuova Funzione Admin (`publius/persone/admin.py`)
- **Funzione**: `stampa_annuario_componenti_case_per_area()`
- **Descrizione**: Organizza i dati per gerarchia di aree invece che per provincia religiosa
- **Logica**:
  - O<PERSON>ene tutte le aree radice (senza parent)
  - Per ogni area radice, processa ricorsivamente tutte le aree figlie usando `get_descendants(include_self=True)`
  - **MODIFICATO**: Raggruppa le case per area e tipo area (invece di area e nazione)
  - **MODIFICATO**: Include tutti i religiosi senza filtri su voci curriculum (rimosso filtro postulanti/novizie)
  - **MODIFICATO**: Utilizza il template DOCX specifico (`religiosi_annuario_per_aree.docx`)

### 2. Nuovo URL (`publius/persone/admin.py`)
- **URL**: `stampaannuariocaseperarea/`
- **Nome**: `persone_religioso_stampaannuariocaseperarea`
- **Metodo**: GET

### 3. Template Aggiornato (`publius/persone/templates/religioso_change_list.html`)
- Aggiunto nuovo pulsante "Ann. Case per Area" nella sezione commentata dei pulsanti di stampa

### 4. Test Aggiunto (`publius/persone/tests/test_admin.py`)
- **Test**: `test_stampa_annuario_componenti_case_per_area()`
- **Verifica**: Che la nuova funzione risponda correttamente (status 200)

## Caratteristiche Tecniche

### Utilizzo MPTT (Modified Preorder Tree Traversal)
- Sfrutta il metodo `get_descendants(include_self=True)` del modello Area
- Permette navigazione efficiente della gerarchia delle aree

### Compatibilità
- **MODIFICATO**: Utilizza il template DOCX specifico `religiosi_annuario_per_aree.docx`
- Mantiene la stessa struttura dati nel contesto
- **MODIFICATO**: Include tutti i religiosi senza esclusioni (rimosso filtro postulanti/novizie)
- **MODIFICATO**: Usa tipo area al posto di nazione per compatibilità con il template

### Organizzazione Dati
- **Livello 1**: Aree radice (senza parent)
- **Livello 2**: Tutte le aree figlie (ricorsivamente)
- **Livello 3**: Raggruppamento per area specifica + tipo area
- **Livello 4**: Case popolate con religiosi
- **Livello 5**: Religiosi per casa (tutti, senza esclusioni)

## File Modificati
1. `publius/persone/admin.py` - Nuova funzione e URL
2. `publius/persone/templates/religioso_change_list.html` - Nuovo pulsante
3. `publius/persone/tests/test_admin.py` - Nuovo test
4. `common/stampe/utils.py` - Aggiunto supporto per template `religiosi_annuario_per_aree`

## Utilizzo
1. Accedere all'admin dei Religiosi in Publius
2. Il pulsante "Ann. Case per Area" sarà disponibile nella toolbar (quando i pulsanti saranno decommentati)
3. La stampa genererà un file DOCX con i dati organizzati per gerarchia di aree

## Note
- La funzione è completamente funzionale e testata
- I pulsanti di stampa nel template sono attualmente commentati
- Per attivare il pulsante, rimuovere i commenti nel template `religioso_change_list.html`
- Il filename generato include il suffisso `_per_area` per distinguerlo dalla stampa originale

## Modifiche Recenti (Versione 2)
- **Rimosso filtro voci curriculum**: La funzione ora include tutti i religiosi senza escludere postulanti e novizie
- **Cambiato raggruppamento**: Organizza per area + tipo area invece di area + nazione
- **Compatibilità template**: Usa il campo `nazione_obj` per contenere il tipo area, mantenendo compatibilità con il template esistente

## Modifiche Recenti (Versione 3)
- **Cambiato template**: Ora utilizza il template specifico `religiosi_annuario_per_aree.docx` invece di `religiosi_annuario_componenti_case.docx`
- **Aggiunto supporto template**: Aggiunto il nuovo template alla funzione `get_template_stampa()` in `common/stampe/utils.py`
