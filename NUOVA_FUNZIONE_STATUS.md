# Nuova Funzione per Identificazione Superiori tramite Status

## Panoramica

È stata creata una nuova funzione `get_elenco_componenti_annuario_per_status()` nel modello `CasaPublius` che identifica i superiori tramite lo **status del religioso** invece che tramite il **tipo di servizio**.

## Motivazione

La funzione originale `get_elenco_componenti_annuario()` identifica i superiori cercando nei servizi del religioso quelli che contengono "superior" nel nome del tipo servizio. La nuova funzione offre un approccio alternativo basato direttamente sullo status del religioso.

## Implementazione

### Nuova Funzione nel Modello CasaPublius

```python
def get_elenco_componenti_annuario_per_status(self, provincia_id=None, voci_curriculum_esclusi=None):
    """
    Versione alternativa di get_elenco_componenti_annuario che identifica i superiori
    tramite lo status del religioso invece che tramite il tipo di servizio.
    
    I superiori sono identificati da status che contengono parole chiave come:
    'superior', 'provinciale', 'generale', 'direttore', 'rettore', 'guardiano', 'priore'
    """
```

### Parole Chiave per Identificazione Superiori

La funzione cerca le seguenti parole chiave nello status del religioso (case-insensitive):

- **superior** - Superiore generico
- **provinciale** - Superiore provinciale
- **generale** - Superiore generale
- **direttore** - Direttore di casa/istituto
- **rettore** - Rettore di seminario/università
- **guardiano** - Guardiano di convento
- **priore** - Priore di monastero
- **ministro** - Ministro generale/provinciale
- **custode** - Custode (es. Terra Santa)
- **vicario** - Vicario generale/provinciale

### Logica di Funzionamento

1. **Recupera religiosi**: Filtra religiosi vivi e non usciti dalla casa
2. **Verifica status**: Per ogni religioso, controlla se il suo status contiene parole chiave di superiore
3. **Classifica**: Separa i religiosi in due categorie:
   - **Superiori**: Status contiene parole chiave → aggiunge "sup." al nome
   - **Religiosi normali**: Status non contiene parole chiave → nome normale
4. **Formatta output**: Restituisce stringa con superiori prima e religiosi normali dopo

## Integrazione nella Stampa

### Modifica nella Funzione di Stampa

La funzione `stampa_annuario_componenti_case_per_area` è stata aggiornata per utilizzare la nuova funzione:

```python
# PRIMA (funzione originale)
elenco_componenti_annuario = casa.get_elenco_componenti_annuario()

# DOPO (nuova funzione)
elenco_componenti_annuario = casa.get_elenco_componenti_annuario_per_status()
```

## Esempi di Utilizzo

### Status che Identificano Superiori

- ✅ "Superiore Provinciale" → Identificato come superiore
- ✅ "Direttore Casa" → Identificato come superiore  
- ✅ "RETTORE Seminario" → Identificato come superiore (case-insensitive)
- ✅ "Guardiano Convento" → Identificato come superiore
- ✅ "Ministro Generale" → Identificato come superiore

### Status che NON Identificano Superiori

- ❌ "Religioso Professo" → Religioso normale
- ❌ "Sacerdote" → Religioso normale
- ❌ "Fratello" → Religioso normale
- ❌ "Novizio" → Religioso normale

### Output di Esempio

```
Fra Giuseppe Bianchi sup.
Fra Mario Rossi sup.
Fra Antonio Neri
Fra Luigi Verdi
```

## Vantaggi della Nuova Funzione

### 1. **Identificazione Diretta**
- Basata direttamente sullo status del religioso
- Non dipende dalla configurazione dei servizi
- Più immediata e intuitiva

### 2. **Flessibilità**
- Supporta multiple parole chiave
- Ricerca case-insensitive
- Facilmente estendibile con nuove parole chiave

### 3. **Semplicità**
- Logica più diretta e comprensibile
- Meno dipendenze da altri modelli
- Manutenzione più semplice

### 4. **Compatibilità**
- Stessa interfaccia della funzione originale
- Parametri identici (`provincia_id`, `voci_curriculum_esclusi`)
- Output nello stesso formato

## Test e Validazione

### Test Implementati

1. **Confronto con funzione originale**: Verifica che la nuova funzione produca risultati coerenti
2. **Test parole chiave**: Verifica che tutte le parole chiave siano riconosciute
3. **Test religiosi normali**: Verifica che religiosi senza status di superiore non siano classificati erroneamente
4. **Test case sensitivity**: Verifica che la ricerca funzioni indipendentemente da maiuscole/minuscole

### Risultati Test

```
✓ Nuova funzione identifica correttamente i superiori tramite status
✓ Tutte le parole chiave funzionano correttamente
✓ Religiosi normali non identificati come superiori
✓ Ricerca case-insensitive funziona
✓ Tutti i 73 test esistenti continuano a passare
```

## Configurazione e Manutenzione

### Aggiungere Nuove Parole Chiave

Per aggiungere nuove parole chiave che identificano superiori, modificare la lista in `get_elenco_componenti_annuario_per_status()`:

```python
parole_chiave_superiore = [
    'superior', 'provinciale', 'generale', 'direttore', 'rettore', 
    'guardiano', 'priore', 'ministro', 'custode', 'vicario',
    'nuova_parola_chiave'  # Aggiungi qui
]
```

### Tornare alla Funzione Originale

Per tornare alla funzione originale basata sui servizi, modificare in `admin.py`:

```python
# Torna alla funzione originale
elenco_componenti_annuario = casa.get_elenco_componenti_annuario()
```

## Conclusioni

La nuova funzione `get_elenco_componenti_annuario_per_status()` offre un metodo alternativo e più diretto per identificare i superiori nelle stampe dell'annuario, basandosi sullo status del religioso piuttosto che sui servizi. Questo approccio è più intuitivo e meno dipendente dalla configurazione di altri modelli, mantenendo piena compatibilità con il sistema esistente.
