from .common import *
import os

DEBUG = True
MATTHAEUS_ENABLED = getenv_bool('MATTHAEUS_ENABLED', True)
CYRENAEUS_ENABLED = getenv_bool('CYRENAEUS_ENABLED', True)
SAULO_ENABLED = getenv_bool('SAULO_ENABLED', True)
PUBLIUS_ENABLED = getenv_bool('PUBLIUS_ENABLED', True)
MARTINUS_ENABLED = getenv_bool('MARTINUS_ENABLED', True)
PETRUS_ENABLED = getenv_bool('PETRUS_ENABLED', True)

EGO_ENABLED = getenv_bool('EGO_ENABLED', True)
DONUM_ENABLED = getenv_bool('DONUM_ENABLED', True)

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.postgresql_psycopg2',
#         'NAME': 'magister',
#         'USER': 'postgres',
#         'PASSWORD': 'postgres',
#         'HOST': 'localhost',
#         'PORT': '5432',
#     }
# }

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.postgresql_psycopg2',
#         'NAME': 'omimed',
#         'USER': 'postgres',
#         'PASSWORD': 'postgres',
#         'HOST': 'localhost',
#         'PORT': '5432',
#     }
# }

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.postgresql_psycopg2',
#         'NAME': 'omimed_nuovo',
#         'USER': 'postgres',
#         'PASSWORD': 'postgres',
#         'HOST': 'localhost',
#         'PORT': '5432',
#     }
# }

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.postgresql_psycopg2',
#         'NAME': 'lazzaristi',
#         'USER': 'postgres',
#         'PASSWORD': 'postgres',
#         'HOST': 'localhost',
#         'PORT': '5432',
#     }
# }

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.postgresql_psycopg2',
#         'NAME': 'magister_demo',
#         'USER': 'postgres',
#         'PASSWORD': 'postgres',
#         'HOST': 'localhost',
#         'PORT': '5432',
#     }
# }

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.postgresql_psycopg2',
#         'NAME': 'comboniane_nuovo',
#         'USER': 'postgres',
#         'PASSWORD': 'postgres',
#         'HOST': 'localhost',
#         'PORT': '5432',
#     }
# }

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql_psycopg2',
        'NAME': 'cottolengo',
        'USER': 'postgres',
        'PASSWORD': 'postgres',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.postgresql_psycopg2',
#         'NAME': 'immacolatine',
#         'USER': 'postgres',
#         'PASSWORD': 'postgres',
#         'HOST': 'localhost',
#         'PORT': '5432',
#     }
# }

INTERNAL_IPS = [
    '127.0.0.1',
]

GENERE_CONGREGAZIONE = 'f'
CONGREGAZIONE_MASCHILE = False
CONGREGAZIONE_FEMMINILE = True

NUMERO_LIVELLI_PIANO_DEI_CONTI = 3

password = os.environ.get('REDIS_PASSWORD', '')
if password:
    password = ':{}@'.format(password)
CELERY_BROKER_URL = 'redis://{}{}:6379'.format(
    password, os.environ.get('REDIS_HOST', 'localhost'))
CELERY_RESULT_BACKEND = CELERY_BROKER_URL

# LOGGING = {
#     'version': 1,
#     'disable_existing_loggers': False,
#     'handlers': {
#         'console': {
#             "level": "DEBUG",
#             'class': 'logging.StreamHandler',
#         },
#     },
#     'loggers': {
#         'django.db.backends': {
#             'handlers': ['console'],
#             'level': 'DEBUG',
#         },
#     },
# }


MIDDLEWARE = ['debug_toolbar.middleware.DebugToolbarMiddleware', ] + MIDDLEWARE
INSTALLED_APPS += ['debug_toolbar', ]

AWS_STORAGE_BUCKET_NAME = os.getenv('AWS_STORAGE_BUCKET_NAME', 'magister')
# VALUTA_DEFAULT = 'CAD'


# TAB E MENU NASCOSTI - indipendentemente dai permessi

# PUBLIUS_TAB_NASCOSTI = os.getenv(
#     'PUBLIUS_HIDDEN_TABS',
#     'curriculum,familiari,pubblicazioni,consigli_generali,consigli_circoscrizione,studi,lingue,assemblee,ricoveri,assegnazioni'
# )
# PUBLIUS_ELENCO_TAB_NASCOSTI = PUBLIUS_TAB_NASCOSTI.split(',')

# ES: PUBLIUS_MENU_NASCOSTI = os.getenv('PUBLIUS_MENU_NASCOSTI', 'persone.familiare')
# PUBLIUS_MENU_NASCOSTI = os.getenv(
#     'PUBLIUS_HIDDEN_MENUS',
#     'persone.curriculum,persone.familiare,persone.pubblicazione,persone.consigliogenerale,persone.consigliocircoscrizione,persone.studio,persone.linguaconosciuta,persone.gradoparentela,persone.lingua,persone.vocecurriculum,persone.assemblea,persone.ricovero,persone.assegnazione'
# )
# PUBLIUS_ELENCO_MENU_NASCOSTI = PUBLIUS_MENU_NASCOSTI.split(',')


DEEPL_AUTH_KEY = 'c0efac74-0330-4d6f-81de-14a5e180fe2e:fx'
ROSETTA_ENABLE_TRANSLATION_SUGGESTIONS = True
