from common.stampe.models import Modello
from django.conf import settings
from os.path import join


def get_template_stampa(tipo_stampa, codice_lingua=None):
    try:
        modello = Modello.objects.get(tipo=tipo_stampa)
        return modello.get_template_modello()
    except Modello.DoesNotExist:
        pass
    nome_template = ''
    cartella_template = ''
    nome_file = ''
    cartella_base = join(settings.BASE_DIR, 'common/templates')
    if tipo_stampa == 'scadenziario_elenco_scadenze':
        nome_template = 'common/templates/common/scadenziario/elenco_scadenze.odt'
    elif tipo_stampa == 'elenco_allegati':
        nome_template = 'common/templates/common/allegati/elenco_allegati.odt'
    # MODELLI PUBLIUS
    elif tipo_stampa == 'catalogo_generale':
        nome_template = 'publius/persone/catalogo_generale.odt'
    elif tipo_stampa == 'elenco_semplice_religiosi':
        nome_template = 'publius/persone/elenco_semplice_religiosi.odt'
    elif tipo_stampa == 'elenco_indirizzi_religiosi':
        nome_template = 'publius/persone/elenco_indirizzi_religiosi.odt'
    elif tipo_stampa == 'elenco_date_religiosi':
        nome_template = 'publius/persone/elenco_date_religiosi.odt'
    elif tipo_stampa == 'elenco_parenti_religiosi':
        nome_template = 'publius/persone/elenco_parenti_religiosi.odt'
    elif tipo_stampa == 'elenco_curriculum_religiosi':
        nome_template = 'publius/persone/elenco_curriculum_religiosi.odt'
    elif tipo_stampa == 'elenco_servizi_religiosi':
        nome_template = 'publius/persone/elenco_servizi_religiosi.odt'
    elif tipo_stampa == 'elenco_studi_religiosi':
        nome_template = 'publius/persone/elenco_studi_religiosi.odt'
    elif tipo_stampa == 'elenco_datisanitari_religiosi':
        nome_template = 'publius/persone/elenco_datisanitari_religiosi.odt'
    elif tipo_stampa == 'elenco_compleanni_religiosi':
        nome_template = 'publius/persone/elenco_compleanni_religiosi.odt'
    elif tipo_stampa == 'rubrica_telefonica_religiosi':
        nome_template = 'publius/persone/rubrica_telefonica_religiosi.odt'
    elif tipo_stampa == 'elenco_defunti_religiosi':
        nome_template = 'publius/persone/elenco_defunti_religiosi.odt'
    elif tipo_stampa == 'ricorrenze_defunti_religiosi':
        nome_template = 'publius/persone/ricorrenze_defunti_religiosi.odt'
    elif tipo_stampa == 'scheda_completa_religiosi':
        cartella_template = '%s/publius/persone/' % cartella_base
        nome_file = 'scheda_completa_religiosi.docx'
        # nome_template = 'common/templates/publius/persone/scheda_completa_religiosi.odt'
    elif tipo_stampa == 'dati_sanitari_religiosi':
        nome_template = 'publius/persone/dati_sanitari_religiosi.odt'
    elif tipo_stampa == 'religiosi_annuario_familiari':
        cartella_template = '%s/publius/persone/' % cartella_base
        nome_file = 'religiosi_annuario_familiari.docx'
    elif tipo_stampa == 'religiosi_annuario_defunti':
        cartella_template = '%s/publius/persone/' % cartella_base
        nome_file = 'religiosi_annuario_defunti.docx'
    elif tipo_stampa == 'religiosi_annuario_componenti_case':
        cartella_template = '%s/publius/persone/' % cartella_base
        nome_file = 'religiosi_annuario_componenti_case.docx'
    elif tipo_stampa == 'religiosi_annuario_nazionalita':
        cartella_template = '%s/publius/persone/' % cartella_base
        nome_file = 'religiosi_annuario_nazionalita.docx'
    elif tipo_stampa == 'religiosi_annuario_per_aree':
        cartella_template = '%s/publius/persone/' % cartella_base
        nome_file = 'religiosi_annuario_per_aree.docx'
    elif tipo_stampa == 'religiosi_quadro_riassuntivo':
        cartella_template = '%s/publius/persone/' % cartella_base
        nome_file = 'religiosi_quadro_riassuntivo.docx'
    # MODELLI PETRUS
    elif tipo_stampa == 'nota_spese_elenco':
        cartella_template = '%s/petrus/spese/' % cartella_base
        nome_file = 'nota_spese_elenco.docx'
    # MODELLI CYRENAEUS
    elif tipo_stampa == 'case_elenco_case_semplice':
        nome_template = 'cyrenaeus/case/elenco_case_semplice.odt'
    elif tipo_stampa == 'immobili_elenco_schede_immobili':
        nome_template = 'cyrenaeus/immobili/elenco_schede_immobili.odt'
    elif tipo_stampa == 'inventario_scheda_veicoli':
        nome_template = 'common/templates/cyrenaeus/inventario/scheda_veicoli.odt'
    elif tipo_stampa == 'inventario_scheda_beni_culturali':
        nome_template = 'common/templates/cyrenaeus/inventario/scheda_beni_culturali.odt'
    elif tipo_stampa == 'immobilizzazioni_schedaammortamento':
        nome_template = 'common/immobilizzazioni/scheda_ammortamento.odt'
    # MODELLI MARTINUS
    elif tipo_stampa == 'donazioni_sollecito':
        cartella_template = '%s/martinus/donazioni/' % cartella_base
        nome_file = 'elenco_solleciti.docx'
    elif tipo_stampa == 'donazioni_ringraziamenti':
        cartella_template = '%s/martinus/donazioni/' % cartella_base
        nome_file = 'elenco_ringraziamenti.docx'
    elif tipo_stampa == 'donazioni_ricevute':
        cartella_template = '%s/martinus/donazioni/' % cartella_base
        nome_file = 'elenco_ricevute.docx'
    # MODELLI MATTHAEUS
    elif tipo_stampa == 'libro_cassa':
        cartella_template = 'matthaeus/partitasemplice/'
        nome_file = 'libro_cassa.odt'
    elif tipo_stampa == 'estratto_conto':
        cartella_template = 'matthaeus/partitasemplice/'
        nome_file = 'estratto_conto.odt'
    elif tipo_stampa == 'libro_giornale':
        cartella_template = 'matthaeus/movimenti/'
        nome_file = 'libro_giornale.odt'
    elif tipo_stampa == 'elenco_partitari':
        cartella_template = '%s/matthaeus/movimenti/' % cartella_base
        nome_file = 'partitari.docx'
    elif tipo_stampa == 'bilancio_conti':
        cartella_template = 'matthaeus/movimenti/'
        nome_file = 'bilancio_conti.odt'
    elif tipo_stampa == 'bilancio_conti_aggregazioni':
        cartella_template = 'matthaeus/movimenti/'
        nome_file = 'bilancio_conti_aggregazioni.odt'
    elif tipo_stampa == 'bilancio_sottoconti':
        cartella_template = 'matthaeus/movimenti/'
        nome_file = 'bilancio_sottoconti.odt'
    elif tipo_stampa == 'bilancio_sottoconti_aggregazioni':
        cartella_template = 'matthaeus/movimenti/'
        nome_file = 'bilancio_sottoconti_aggregazioni.odt'
    elif tipo_stampa == 'primanota_analitica':
        cartella_template = 'matthaeus/movimenti/'
        nome_file = 'stampa_analitica.odt'
    elif tipo_stampa == 'primanota_sintetica':
        cartella_template = 'matthaeus/movimenti/'
        nome_file = 'stampa_sintetica.odt'
    elif tipo_stampa == 'stato_patrimoniale':
        cartella_template = 'matthaeus/movimenti/'
        nome_file = 'stato_patrimoniale.odt'
    elif tipo_stampa == 'stato_patrimoniale_parziali':
        cartella_template = 'matthaeus/movimenti/'
        nome_file = 'stato_patrimoniale_parziali.odt'
    elif tipo_stampa == 'stato_patrimoniale_clienti_fornitori':
        cartella_template = 'matthaeus/movimenti/'
        nome_file = 'stato_patrimoniale_clienti_fornitori.odt'
    elif tipo_stampa == 'bilancio_comparativo':
        cartella_template = '%s/matthaeus/movimenti/' % cartella_base
        nome_file = 'bilancio_comparativo.docx'
    elif tipo_stampa == 'rendiconto_finanziario':
        cartella_template = 'matthaeus/movimenti/'
        nome_file = 'rendiconto_finanziario.odt'
    elif tipo_stampa == 'bilancio_riclassificato':
        cartella_template = 'matthaeus/movimenti/'
        nome_file = 'bilancio_riclassificato.odt'
    elif tipo_stampa == 'piano_dei_conti':
        nome_template = 'common/anagraficabase/piano_dei_conti.odt'
    elif tipo_stampa == 'elenco_partitari_anagrafica':
        cartella_template = '%s/matthaeus/movimenti/' % cartella_base
        nome_file = 'elenco_partitari_anagrafica.docx'
    elif tipo_stampa == 'elenco_mastrini':
        cartella_template = '%s/matthaeus/movimenti/' % cartella_base
        nome_file = 'elenco_mastrini.docx'
    elif tipo_stampa == 'elenco_mastrini_centri_di_costo':
        cartella_template = '%s/matthaeus/movimenti/' % cartella_base
        nome_file = 'elenco_mastrini_centri_di_costo.docx'
    elif tipo_stampa == 'libro_giornale_elenco':
        cartella_template = '%s/matthaeus/movimenti/' % cartella_base
        nome_file = 'libro_giornale_elenco.docx'
    if not nome_template:
        if cartella_template and nome_file:
            if codice_lingua:
                nome_template = '%s%s_%s' % (cartella_template, codice_lingua.upper(), nome_file)
            else:
                nome_template = '%s%s' % (cartella_template, nome_file)
    return nome_template
