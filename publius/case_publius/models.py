from datetime import date

from django.utils.translation import ugettext_lazy as _
from django.urls import reverse
from django.db import models

from cyrenaeus.case.models import Casa


class CasaPublius(Casa):

    class Meta:
        proxy = True
        verbose_name_plural = _('Case')
        verbose_name = _('Casa')

    def get_url(self):
        url = reverse('publius:case_publius_casapublius_change', args=(self.id,))
        return url

    def get_riga_indirizzo(self):
        indirizzo = '%s - ' % self.indirizzo
        if self.cap:
            indirizzo += ' %s' % self.cap
        if self.citta:
            indirizzo += ' %s' % self.citta
        if self.provincia:
            indirizzo += ' (%s)' % self.provincia.codice
        if self.nazione:
            indirizzo += ' - %s' % self.nazione
        return indirizzo

    def get_riga_indirizzo_annuario(self):
        indirizzo = '%s\n' % self.indirizzo
        if self.cap:
            indirizzo += '%s' % self.cap
        if self.citta:
            indirizzo += ' %s' % self.citta
        if self.provincia:
            indirizzo += ' %s' % self.provincia.codice
        if self.nazione:
            indirizzo += ' (%s)' % self.nazione.codice or self.nazione
        return indirizzo

    def get_riga_contatti(self):
        contatti = ''
        if self.telefono:
            contatti += 'tel.: %s ' % self.telefono
        if self.fax:
            contatti += 'fax: %s ' % self.fax
        if self.email:
            contatti += 'email: %s' % self.email
        if self.email_2:
            contatti += 'email(2): %s' % self.email_2
        return contatti

    def get_riga_contatti_annuario(self):
        contatti = ''
        if self.telefono:
            contatti += 'tel.: %s\n' % self.telefono
        if self.fax:
            contatti += 'fax: %s\n' % self.fax
        if self.email:
            contatti += 'email: %s' % self.email
        return contatti

    def get_dati_anagrafici_annuario(self):
        dati_annuario = ''
        if self.descrizione:
            dati_annuario = '%s\n' % self.descrizione
        dati_annuario += '%s\n%s\n' % (self.get_riga_indirizzo_annuario(), self.get_riga_contatti_annuario())
        return dati_annuario

    def get_dati_fondazione_annuario(self):
        dati_annuario = ''
        if self.data_apertura:
            dati_annuario += 'Fond. %s' % self.data_apertura.strftime('%d/%m/%Y')
        if self.attivita_svolta:
            dati_annuario += ' %s\n' % self.attivita_svolta
        return dati_annuario

    def get_elenco_componenti_annuario(self, provincia_id=None, voci_curriculum_esclusi=None):
        from publius.persone.models import Religioso, TipoServizio
        stringa_religiosi = ''
        stringa_superiori = ''
        tipi_servizio_superiore = TipoServizio.objects.filter(nome__icontains='superior')
        elenco_religiosi = Religioso.objects.filter(data_morte__isnull=True, data_uscita__isnull=True).order_by('appellativo', 'cognome', 'nome')
        if voci_curriculum_esclusi:
            elenco_religiosi = elenco_religiosi.exclude(status_religioso__in=voci_curriculum_esclusi)
        if provincia_id:
            elenco_religiosi_casa = elenco_religiosi.filter(casa_attuale=self, provincia_religiosa_id=provincia_id)
        else:
            elenco_religiosi_casa = elenco_religiosi.filter(casa_attuale=self)
        if elenco_religiosi_casa:
            for religioso in elenco_religiosi_casa:
                superiore = False
                if religioso.get_elenco_servizi():
                    ultimo_servizio = religioso.get_elenco_servizi()[0]
                    if ultimo_servizio.tipo_servizio in tipi_servizio_superiore:
                        superiore = True
                if superiore:
                    stringa_superiori += '\n%s sup.' % religioso
                else:
                    if stringa_religiosi:
                        stringa_religiosi += '\n'
                    stringa_religiosi += '%s' % religioso
        if not stringa_superiori and not stringa_religiosi:
            return ''
        return '%s\n%s' % (stringa_superiori, stringa_religiosi)

    def get_elenco_componenti_annuario_per_status(self, provincia_id=None, voci_curriculum_esclusi=None, con_formattazione=False):
        """
        Versione alternativa di get_elenco_componenti_annuario che identifica i superiori
        tramite lo status del religioso invece che tramite il tipo di servizio.

        I superiori sono identificati da status che contengono parole chiave come:
        'superior', 'provinciale', 'generale', 'direttore', 'rettore', 'guardiano', 'priore'

        Args:
            provincia_id: ID della provincia (non utilizzato in questa versione)
            voci_curriculum_esclusi: Voci del curriculum da escludere (non utilizzato in questa versione)
            con_formattazione: Se True, restituisce un oggetto RichText per la formattazione DOCX
        """
        from publius.persone.models import Religioso, Curriculum

        # Inizializza variabili in base al tipo di formattazione richiesta
        if con_formattazione:
            lista_religiosi = []
            lista_superiori = []
        else:
            stringa_religiosi = ''
            stringa_superiori = ''

        # Parole chiave che identificano ruoli di superiore nello status
        parole_chiave_superiore = [
            'superior', 'provinciale', 'generale', 'direttore', 'rettore',
            'guardiano', 'priore', 'ministro', 'custode', 'vicario'
        ]

        elenco_religiosi = Religioso.objects.filter(
            data_morte__isnull=True,
            data_uscita__isnull=True
        ).select_related('status').order_by('appellativo', 'cognome', 'nome')

        if voci_curriculum_esclusi:
            elenco_religiosi = elenco_religiosi.exclude(status_religioso__in=voci_curriculum_esclusi)

        if provincia_id:
            elenco_religiosi_casa = elenco_religiosi.filter(
                casa_attuale=self,
                provincia_religiosa_id=provincia_id
            )
        else:
            elenco_religiosi_casa = elenco_religiosi.filter(casa_attuale=self)

        if elenco_religiosi_casa:
            # Ottimizzazione: estrai tutte le date di professione in una sola query
            religiosi_ids = [r.id for r in elenco_religiosi_casa]
            date_professioni = self._get_date_professioni_bulk(religiosi_ids)

            # Ottimizzazione: estrai tutte le date di trasferimento nella casa attuale in una sola query
            date_trasferimenti = self._get_date_trasferimenti_bulk(religiosi_ids)

            # Ottimizzazione: estrai tutti i titoli di studio e servizi aperti in blocco
            titoli_studio_bulk = self._get_titoli_studio_bulk(religiosi_ids)
            servizi_aperti_bulk = self._get_servizi_aperti_bulk(religiosi_ids)

            for religioso in elenco_religiosi_casa:
                superiore = False

                # Verifica se lo status del religioso indica un ruolo di superiore
                if religioso.status and religioso.status.nome:
                    status_nome_lower = religioso.status.nome.lower()
                    for parola_chiave in parole_chiave_superiore:
                        if parola_chiave in status_nome_lower:
                            superiore = True
                            break

                # Crea la descrizione dettagliata del religioso usando i dati pre-caricati
                # Passa il parametro superiore per includere lo status nella descrizione
                if con_formattazione:
                    descrizione_religioso = self._get_descrizione_religioso_con_richtext(
                        religioso, date_professioni.get(religioso.id, {}), superiore,
                        date_trasferimenti.get(religioso.id)
                    )
                else:
                    descrizione_religioso = self._get_descrizione_religioso_dettagliata_ottimizzata(
                        religioso, date_professioni.get(religioso.id, {}), superiore,
                        date_trasferimenti.get(religioso.id)
                    )

                # Aggiungi titoli di studio e servizi aperti
                dettagli_aggiuntivi = self._get_dettagli_studio_servizi(
                    religioso.id, titoli_studio_bulk, servizi_aperti_bulk
                )

                # Combina descrizione religioso con dettagli aggiuntivi
                if con_formattazione:
                    # Con formattazione RichText
                    descrizione_completa = descrizione_religioso
                    if dettagli_aggiuntivi:
                        descrizione_completa.add(dettagli_aggiuntivi)

                    # Aggiungi alla lista appropriata
                    if superiore:
                        lista_superiori.append(descrizione_completa)
                    else:
                        lista_religiosi.append(descrizione_completa)
                else:
                    # Senza formattazione (stringa)
                    descrizione_completa = descrizione_religioso
                    if dettagli_aggiuntivi:
                        descrizione_completa += dettagli_aggiuntivi

                    if superiore:
                        stringa_superiori += '\n%s' % descrizione_completa
                    else:
                        if stringa_religiosi:
                            stringa_religiosi += '\n'
                        stringa_religiosi += '%s' % descrizione_completa

        # Restituisci il risultato in base al tipo di formattazione
        if con_formattazione:
            # Con formattazione, combina le liste in un unico RichText
            from docxtpl import RichText
            risultato_rt = RichText()

            # Aggiungi superiori
            for i, superiore_rt in enumerate(lista_superiori):
                if i > 0:
                    risultato_rt.add('\n')
                risultato_rt.add(superiore_rt)

            # Aggiungi religiosi
            for i, religioso_rt in enumerate(lista_religiosi):
                if i > 0 or lista_superiori:
                    risultato_rt.add('\n')
                risultato_rt.add(religioso_rt)

            return risultato_rt
        else:
            # Senza formattazione, restituisci stringa
            if not stringa_superiori and not stringa_religiosi:
                return ''
            return '%s\n%s' % (stringa_superiori, stringa_religiosi)

    def _get_descrizione_religioso_dettagliata(self, religioso):
        """
        Crea una descrizione dettagliata del religioso con:
        Cognome e nome, nata il, data prima professione, data professione perpetua
        """
        # Cognome e nome
        descrizione = '%s %s' % (religioso.cognome, religioso.nome)

        # Data di nascita
        if religioso.data_nascita:
            descrizione += ', nata il %s' % religioso.data_nascita.strftime('%d/%m/%Y')

        # Trova le date di prima professione e professione perpetua dal curriculum
        data_prima_professione = self._get_data_prima_professione(religioso)
        data_professione_perpetua = self._get_data_professione_perpetua(religioso)

        if data_prima_professione:
            descrizione += ', prima prof. %s' % data_prima_professione

        if data_professione_perpetua:
            descrizione += ', prof. perpetua %s' % data_professione_perpetua

        return descrizione

    def _get_data_prima_professione(self, religioso):
        """
        Trova la data della prima professione (voti temporanei) dal curriculum
        """
        from publius.persone.models import Curriculum

        curriculum_prima_prof = Curriculum.objects.filter(
            religioso=religioso,
            voce_curriculum__tipologia_percorso='voti_temporanei'
        ).order_by('data_curriculum').first()

        if curriculum_prima_prof and curriculum_prima_prof.data_curriculum:
            return curriculum_prima_prof.data_curriculum.strftime('%d/%m/%Y')
        return None

    def _get_data_professione_perpetua(self, religioso):
        """
        Trova la data della professione perpetua (voti perpetui) dal curriculum
        """
        from publius.persone.models import Curriculum

        curriculum_prof_perpetua = Curriculum.objects.filter(
            religioso=religioso,
            voce_curriculum__tipologia_percorso='voti_perpetui'
        ).order_by('data_curriculum').first()

        if curriculum_prof_perpetua and curriculum_prof_perpetua.data_curriculum:
            return curriculum_prof_perpetua.data_curriculum.strftime('%d/%m/%Y')
        return None

    def _get_date_professioni_bulk(self, religiosi_ids):
        """
        Estrae tutte le date di prima professione e professione perpetua
        per una lista di religiosi in una sola query ottimizzata.

        Returns:
            dict: {religioso_id: {'prima_professione': 'DD/MM/YYYY', 'professione_perpetua': 'DD/MM/YYYY'}}
        """
        from publius.persone.models import Curriculum

        # Query unica per ottenere tutti i curriculum di voti temporanei e perpetui
        curriculum_queryset = Curriculum.objects.filter(
            religioso_id__in=religiosi_ids,
            voce_curriculum__tipologia_percorso__in=['voti_temporanei', 'voti_perpetui'],
            data_curriculum__isnull=False
        ).select_related('voce_curriculum').order_by('religioso_id', 'data_curriculum')

        # Organizza i dati per religioso
        date_professioni = {}
        for curriculum in curriculum_queryset:
            religioso_id = curriculum.religioso_id
            if religioso_id not in date_professioni:
                date_professioni[religioso_id] = {}

            tipologia = curriculum.voce_curriculum.tipologia_percorso
            data_formattata = curriculum.data_curriculum.strftime('%d/%m/%Y')

            # Prendi solo la prima data per ogni tipologia (order_by data_curriculum)
            if tipologia == 'voti_temporanei' and 'prima_professione' not in date_professioni[religioso_id]:
                date_professioni[religioso_id]['prima_professione'] = data_formattata
            elif tipologia == 'voti_perpetui' and 'professione_perpetua' not in date_professioni[religioso_id]:
                date_professioni[religioso_id]['professione_perpetua'] = data_formattata

        return date_professioni

    def _get_date_trasferimenti_bulk(self, religiosi_ids):
        """
        Estrae tutte le date dell'ultimo trasferimento nella casa attuale
        per una lista di religiosi in una sola query ottimizzata.

        Returns:
            dict: {religioso_id: 'DD/MM/YYYY'} - data dell'ultimo trasferimento nella casa attuale
        """
        from publius.persone.models import Trasferimento

        # Query per ottenere tutti i trasferimenti verso la casa attuale per i religiosi specificati
        trasferimenti_queryset = Trasferimento.objects.filter(
            religioso_id__in=religiosi_ids,
            a_provincia=self,  # Trasferimenti verso questa casa
            data_trasferimento__isnull=False
        ).select_related('religioso').order_by('religioso_id', '-data_trasferimento')

        # Organizza i dati per religioso (prende solo il più recente per ogni religioso)
        date_trasferimenti = {}
        for trasferimento in trasferimenti_queryset:
            religioso_id = trasferimento.religioso_id
            if religioso_id not in date_trasferimenti:
                # Prende solo il primo (più recente) trasferimento per ogni religioso
                date_trasferimenti[religioso_id] = trasferimento.data_trasferimento.strftime('%d/%m/%Y')

        return date_trasferimenti

    def _get_titoli_studio_bulk(self, religiosi_ids):
        """
        Estrae tutti i titoli di studio per una lista di religiosi in una sola query ottimizzata.

        Returns:
            dict: {religioso_id: [lista_titoli_studio]}
        """
        from publius.persone.models import Studio

        # Query unica per ottenere tutti i titoli di studio
        studi_queryset = Studio.objects.filter(
            religioso_id__in=religiosi_ids
        ).select_related('tipo_studio').order_by('religioso_id', 'data_studio')

        # Organizza i dati per religioso
        titoli_studio = {}
        for studio in studi_queryset:
            religioso_id = studio.religioso_id
            if religioso_id not in titoli_studio:
                titoli_studio[religioso_id] = []

            # Crea descrizione del titolo di studio
            descrizione_studio = ''
            if studio.tipo_studio:
                descrizione_studio = studio.tipo_studio.nome
            if studio.qualifica:
                if descrizione_studio:
                    descrizione_studio += f' - {studio.qualifica}'
                else:
                    descrizione_studio = studio.qualifica
            if studio.istituto:
                if descrizione_studio:
                    descrizione_studio += f' ({studio.istituto})'
                else:
                    descrizione_studio = studio.istituto

            if descrizione_studio:
                titoli_studio[religioso_id].append(descrizione_studio)

        return titoli_studio

    def _get_servizi_aperti_bulk(self, religiosi_ids):
        """
        Estrae tutti i servizi aperti (senza data fine) per una lista di religiosi in una sola query ottimizzata.

        Returns:
            dict: {religioso_id: [lista_servizi_aperti]}
        """
        from publius.persone.models import Servizio

        # Query unica per ottenere tutti i servizi aperti
        servizi_queryset = Servizio.objects.filter(
            religioso_id__in=religiosi_ids,
            data_fine__isnull=True
        ).select_related('tipo_servizio', 'presso').order_by('religioso_id', 'data_inizio')

        # Organizza i dati per religioso
        servizi_aperti = {}
        for servizio in servizi_queryset:
            religioso_id = servizio.religioso_id
            if religioso_id not in servizi_aperti:
                servizi_aperti[religioso_id] = []

            # Crea descrizione del servizio
            descrizione_servizio = ''

            # Data inizio
            if servizio.data_inizio:
                descrizione_servizio = servizio.data_inizio.strftime('%d/%m/%Y')

            # Tipo servizio
            if servizio.tipo_servizio:
                if descrizione_servizio:
                    descrizione_servizio += f', {servizio.tipo_servizio.nome}'
                else:
                    descrizione_servizio = servizio.tipo_servizio.nome

            # Specifica
            if servizio.specifica:
                if descrizione_servizio:
                    descrizione_servizio += f', {servizio.specifica}'
                else:
                    descrizione_servizio = servizio.specifica

            # Presso
            if servizio.presso:
                if descrizione_servizio:
                    descrizione_servizio += f', presso {servizio.presso.nome}'
                else:
                    descrizione_servizio = f'presso {servizio.presso.nome}'

            if descrizione_servizio:
                servizi_aperti[religioso_id].append(descrizione_servizio)

        return servizi_aperti

    def _get_dettagli_studio_servizi(self, religioso_id, titoli_studio_bulk, servizi_aperti_bulk):
        """
        Crea la stringa con i dettagli di studio e servizi per un religioso.

        Returns:
            str: Stringa formattata con titoli di studio e servizi aperti indentati
        """
        dettagli = ''

        # Titoli di studio
        titoli = titoli_studio_bulk.get(religioso_id, [])
        if titoli:
            dettagli += '\n    TITOLI DI STUDIO:'
            for titolo in titoli:
                dettagli += f'\n    {titolo}'

        # Servizi aperti
        servizi = servizi_aperti_bulk.get(religioso_id, [])
        if servizi:
            dettagli += '\n    SERVIZI APERTI:'
            for servizio in servizi:
                dettagli += f'\n    {servizio}'

        return dettagli

    def _get_descrizione_religioso_dettagliata_ottimizzata(self, religioso, date_professioni, superiore=False, data_trasferimento=None):
        """
        Versione ottimizzata di _get_descrizione_religioso_dettagliata che usa
        dati pre-caricati invece di fare query per ogni religioso.

        Args:
            religioso: Oggetto Religioso
            date_professioni: dict con chiavi 'prima_professione' e 'professione_perpetua'
            superiore: bool che indica se il religioso è un superiore
            data_trasferimento: str con la data del trasferimento nella casa attuale (formato DD/MM/YYYY)
        """
        # Cognome e nome
        descrizione = '%s' % religioso.get_nome_stampa()

        # Se è un superiore, aggiungi lo status in maiuscolo tra parentesi dopo il nome
        if superiore:
            status_maiuscolo = religioso.status.nome.upper() if religioso.status and religioso.status.nome else 'SUPERIORE'
            descrizione += ' (%s)' % status_maiuscolo

        # Data di nascita
        if religioso.data_nascita:
            descrizione += ', nata il %s' % religioso.data_nascita.strftime('%d/%m/%Y')

        # Date di professione dai dati pre-caricati
        data_prima_professione = date_professioni.get('prima_professione')
        data_professione_perpetua = date_professioni.get('professione_perpetua')

        if data_prima_professione:
            descrizione += ', prima prof. %s' % data_prima_professione

        if data_professione_perpetua:
            descrizione += ', prof. perpetua %s' % data_professione_perpetua

        # Aggiungi la data del trasferimento nella casa attuale se disponibile
        if data_trasferimento:
            descrizione += ', trasf. %s' % data_trasferimento

        return descrizione

    def _get_descrizione_religioso_con_richtext(self, religioso, date_professioni, superiore=False, data_trasferimento=None):
        """
        Versione con formattazione RichText della descrizione del religioso.
        Il nome viene formattato in grassetto.

        Args:
            religioso: Oggetto Religioso
            date_professioni: dict con chiavi 'prima_professione' e 'professione_perpetua'
            superiore: bool che indica se il religioso è un superiore
            data_trasferimento: str con la data del trasferimento nella casa attuale (formato DD/MM/YYYY)
        """
        from docxtpl import RichText

        # Crea oggetto RichText
        descrizione_rt = RichText()

        # Nome in grassetto
        nome_completo = religioso.get_nome_stampa()
        descrizione_rt.add(nome_completo, bold=True)

        # Se è un superiore, aggiungi lo status in maiuscolo tra parentesi dopo il nome (anche in grassetto)
        if superiore:
            status_maiuscolo = religioso.status.nome.upper() if religioso.status and religioso.status.nome else 'SUPERIORE'
            descrizione_rt.add(' (', bold=True)
            descrizione_rt.add(status_maiuscolo, bold=True)
            descrizione_rt.add(')', bold=True)

        # Data di nascita (non in grassetto)
        if religioso.data_nascita:
            descrizione_rt.add(', nata il %s' % religioso.data_nascita.strftime('%d/%m/%Y'))

        # Date di professione (non in grassetto)
        data_prima_professione = date_professioni.get('prima_professione')
        data_professione_perpetua = date_professioni.get('professione_perpetua')

        if data_prima_professione:
            descrizione_rt.add(', prima prof. %s' % data_prima_professione)

        if data_professione_perpetua:
            descrizione_rt.add(', prof. perpetua %s' % data_professione_perpetua)

        # Data del trasferimento (non in grassetto)
        if data_trasferimento:
            descrizione_rt.add(', trasf. %s' % data_trasferimento)

        return descrizione_rt


class Annotazione(models.Model):
    casa = models.ForeignKey(
        CasaPublius, verbose_name=_('casa'), on_delete=models.CASCADE,
    )
    data_annotazione = models.DateField(_('Data Annotazione'), default=date.today)
    annotazione = models.TextField(_('annotazione'))

    class Meta:
        verbose_name_plural = _('diario')
        verbose_name = _('diario')
        ordering = ('-data_annotazione', )

    def __str__(self):
        return _('diario del %s') % self.data_annotazione


class MinisteroReligioso(models.Model):
    nome = models.CharField(_('nome'), max_length=200)
    descrizione = models.CharField(_('descrizione'), max_length=200, null=True, blank=True)

    class Meta:
        verbose_name_plural = _('ministeri religiosi')
        verbose_name = _('ministero religioso')
        ordering = ('nome', 'descrizione')

    def __str__(self):
        return self.nome


class Ministero(models.Model):
    casa = models.ForeignKey(
        CasaPublius, verbose_name=_('casa'), on_delete=models.CASCADE,
    )
    data_iniziale = models.DateField(_('Data iniziale'), default=date.today)
    data_finale = models.DateField(_('Data finale'), null=True, blank=True)
    ministero_religioso = models.ForeignKey(
        MinisteroReligioso, verbose_name=_('ministero religioso'), on_delete=models.CASCADE,
    )
    note = models.TextField(_('note'))

    class Meta:
        verbose_name_plural = _('ministeri')
        verbose_name = _('ministero')
        ordering = ('-data_iniziale', '-data_finale')

    def __str__(self):

        return _('%s - %s') % (self.data_iniziale, self.ministero_religioso)
