import unittest
from django.test import TestCase
from django import setup
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings.test')
setup()

from django.urls import reverse
from common.authentication.tests.factories import UtenteMagisterFactory
from . import factories


class AppAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))

    def test_app_index(self):
        url = reverse('publius:app_list', args=('persone',))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class ReligiosoAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('publius:persone_religioso_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        obj = factories.ReligiosoFactory()
        url = reverse('publius:persone_religioso_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)

    def test_add(self):
        url = reverse('publius:persone_religioso_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        religioso = factories.ReligiosoFactory()
        url = reverse('publius:persone_religioso_delete', args=(religioso.id,))
        response = self.client.post(url)
        self.assertEqual(response.status_code, 302)

    def test_stampa_annuario_componenti_case_per_area(self):
        url = reverse('publius:persone_religioso_stampaannuariocaseperarea')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class CurriculumAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('publius:persone_curriculum_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        obj = factories.CurriculumFactory()
        url = reverse('publius:persone_curriculum_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('publius:persone_curriculum_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        religioso = factories.CurriculumFactory()
        url = reverse('publius:persone_curriculum_delete', args=(religioso.id,))
        response = self.client.post(url)
        self.assertEqual(response.status_code, 200)


class AssenzaAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('publius:persone_assenza_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        obj = factories.AssenzaFactory()
        url = reverse('publius:persone_assenza_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('publius:persone_assenza_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        assenza = factories.AssenzaFactory()
        url = reverse('publius:persone_assenza_delete', args=(assenza.id,))
        response = self.client.post(url)
        self.assertEqual(response.status_code, 200)


class ViaggioAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('publius:persone_viaggio_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        obj = factories.ViaggioFactory()
        url = reverse('publius:persone_viaggio_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('publius:persone_viaggio_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        viaggio = factories.ViaggioFactory()
        url = reverse('publius:persone_viaggio_delete', args=(viaggio.id,))
        response = self.client.post(url)
        self.assertEqual(response.status_code, 200)


class GruppoCommissioneAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('publius:persone_gruppocommissione_changelist')

    def test_list(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = dict(q='text')
        response = self.client.get(self.list, data)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        obj = factories.GruppoCommissioneFactory()
        url = reverse('publius:persone_gruppocommissione_change', args=(obj.pk,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('publius:persone_gruppocommissione_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
    
    def test_delete(self):
        gruppo = factories.GruppoCommissioneFactory()
        url = reverse('publius:persone_gruppocommissione_delete', args=(gruppo.id,))
        response = self.client.post(url)
        self.assertEqual(response.status_code, 200)


class ServizioAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('publius:persone_servizio_changelist')
        self.servizio = factories.ServizioFactory()

    def test_list(self):
        url = reverse('publius:persone_servizio_changelist')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = {'q': 'text'}
        url = reverse('publius:persone_servizio_changelist')
        response = self.client.get(url, data)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        url = reverse('publius:persone_servizio_change', args=(self.servizio.id,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('publius:persone_servizio_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        url = reverse('publius:persone_servizio_delete', args=(self.servizio.id,))
        response = self.client.post(url)
        self.assertEqual(response.status_code, 200)


class AssegnazioneAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('publius:persone_assegnazione_changelist')
        self.assegnazione = factories.AssegnazioneFactory()

    def test_list(self):
        url = reverse('publius:persone_assegnazione_changelist')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = {'q': 'text'}
        url = reverse('publius:persone_assegnazione_changelist')
        response = self.client.get(url, data)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        url = reverse('publius:persone_assegnazione_change', args=(self.assegnazione.id,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('publius:persone_assegnazione_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        url = reverse('publius:persone_assegnazione_delete', args=(self.assegnazione.id,))
        response = self.client.post(url)
        self.assertEqual(response.status_code, 200)


class FamiliareAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('publius:persone_familiare_changelist')
        self.familiare = factories.FamiliareFactory()

    def test_list(self):
        url = reverse('publius:persone_familiare_changelist')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = {'q': 'text'}
        url = reverse('publius:persone_familiare_changelist')
        response = self.client.get(url, data)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        url = reverse('publius:persone_familiare_change', args=(self.familiare.id,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('publius:persone_familiare_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        url = reverse('publius:persone_familiare_delete', args=(self.familiare.id,))
        response = self.client.post(url)
        self.assertEqual(response.status_code, 200)


class PubblicazioneAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('publius:persone_familiare_changelist')
        self.pubblicazione = factories.PubblicazioneFactory()

    def test_list(self):
        url = reverse('publius:persone_pubblicazione_changelist')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = {'q': 'text'}
        url = reverse('publius:persone_pubblicazione_changelist')
        response = self.client.get(url, data)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        url = reverse('publius:persone_pubblicazione_change', args=(self.pubblicazione.id,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('publius:persone_pubblicazione_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        url = reverse('publius:persone_pubblicazione_delete', args=(self.pubblicazione.id,))
        response = self.client.post(url)
        self.assertEqual(response.status_code, 200)


class RipresaVocazionaleAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('publius:persone_ripresavocazionale_changelist')
        self.ripresa = factories.RipresaVocazionaleFactory()

    def test_list(self):
        url = reverse('publius:persone_ripresavocazionale_changelist')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = {'q': 'text'}
        url = reverse('publius:persone_ripresavocazionale_changelist')
        response = self.client.get(url, data)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        url = reverse('publius:persone_ripresavocazionale_change', args=(self.ripresa.id,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('publius:persone_ripresavocazionale_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        url = reverse('publius:persone_ripresavocazionale_delete', args=(self.ripresa.id,))
        response = self.client.post(url)
        self.assertEqual(response.status_code, 200)


class RateoPensioneAdminTest(TestCase):
    def setUp(self):
        UtenteMagisterFactory(username='test')
        self.assertTrue(self.client.login(username='test', password='pass'))
        self.list = reverse('publius:persone_rateopensione_changelist')
        self.rateo = factories.RateoPensioneFactory()

    def test_list(self):
        url = reverse('publius:persone_rateopensione_changelist')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_search(self):
        data = {'q': 'text'}
        url = reverse('publius:persone_rateopensione_changelist')
        response = self.client.get(url, data)
        self.assertEqual(response.status_code, 200)

    def test_detail(self):
        url = reverse('publius:persone_rateopensione_change', args=(self.rateo.id,))
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_add(self):
        url = reverse('publius:persone_rateopensione_add')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_delete(self):
        url = reverse('publius:persone_rateopensione_delete', args=(self.rateo.id,))
        response = self.client.post(url)
        self.assertEqual(response.status_code, 200)


