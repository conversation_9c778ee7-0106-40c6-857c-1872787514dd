#!/usr/bin/env python
# -*- coding: utf-8 -*-
from os.path import exists
from os.path import join
import openpyxl
from datetime import date

from django.contrib import admin
from django.db.models import Q
from django.utils.translation import ugettext_lazy as _
from django.utils.html import mark_safe
from django.conf import settings
from django.template import Context
from django.conf.urls import url
from django.contrib.admin import Simple<PERSON>ist<PERSON>ilter
from django.contrib.admin.filters import RelatedOnlyFieldListFilter
from django.http import HttpResponse
from django.template.defaultfilters import slugify
from django.utils import timezone
from django.contrib import messages
from django.core.files.temp import tempfile
from django.db.models import Count, Prefetch

from suit.admin import RelatedFieldAdmin, get_related_field
from import_export.admin import ImportMixin
from django_admin_multiple_choice_list_filter.list_filters import MultipleChoiceList<PERSON>ilter
from webodt.shortcuts import render_to_response as webodt_render_to_response
from preferences import preferences
from secretary import Renderer
from common.datefilter.filters import DateRangeFilter
from docxtpl import DocxTemplate
from docxtpl import InlineImage
from docx.shared import Mm

from publius.admin import site, PubliusModelAdmin
from publius.persone import forms
from publius.case_publius.models import CasaPublius
from publius.persone.models import (
    Religioso, Curriculum, VoceCurriculum, Status,
    GruppoAppartenenza, TipoDocumento, TipoServizio,
    TipoStudio, ProvinciaReligiosa, RegioneReligiosa,
    Lingua, Servizio, Documento, Studio, Pubblicazione,
    Familiare, GradoParentela, Assenza, Viaggio,
    Ricovero, Assegnazione, Assemblea,
    LinguaConosciuta, GruppoCommissione, Trasferimento,
    ConsiglioCircoscrizione, ConsiglioGenerale,
    ProfiloBiografia, CategoriaPubblicazione,
    TipoCommissione, Benemerenza, TipoRipresa, RipresaVocazionale,
    RateoPensione,TipoPensione, ModalitaIncasso,
    PersoneEconomato, PosizioneCanonica, Incarico, TipoDomicilio
)
from publius.persone.resources import (
    ReligiosoResource, StudioResource, AssenzaResource,
    FamiliareResource, ServizioResource, DocumentoResource, ServizioImportResource,
    CurriculumResource, TrasferimentoResource,
    AssegnazioneResource, ProvinciaReligiosaResource,
    AssembleaResource, ConsiglioCircoscrizioneResource,
    ConsiglioGeneraleResource, ProfiloBiografiaResource,
    PubblicazioneResource, GruppoCommissioneResource,
    BenemerenzaResource, RipresaVocazionaleResource,
    RateoPensioneResource, TipoServizioResource, TipoStudioResource,
    TrasferimentoImportResource, ReligiosoImportResource, TipoDomicilioResource, 
    StudioExportResource, CurriculumImportResource
)
from common.utils.admin import BaseTipoAdmin
from common.utils.admin import InlineEditLinkMixin
from common.stampe.utils import get_template_stampa
from common.allegati.admin import DocumentoAllegatoInlinesAddOnly, DocumentoAllegatoInlinesNoAdd
from common.anagraficabase.models import Stato, Area, TipoArea
from common.allegati.models import DocumentoAllegato
from publius.case_publius.models import CasaPublius
from common.scadenziario.admin import ScadenzaPubliusInline
from common.scadenziario.models import Scadenza
from publius.persone.views import StatusReligiosoPersoneView, FasceEtaReligiosiView
from publius.persone.views import StatusPersoneView, ProvinciaReligiosaView
from publius.persone.views import ReligiosiPerCasaView
from publius.persone.utils import get_percentuale_decessi_uscite_fasce, get_religiosi_presenti_alla_data
from publius.dashboards_widgets import get_elenco_religiosi_filtrato
from cyrenaeus.case.models import TipoCasa


class MagisterMultipleListFilter(MultipleChoiceListFilter):
    template = 'magister_multiple_filter.html'

    def choices(self, changelist):
        def amend_query_string(include=None, exclude=None):
            selections = self.value_as_list()
            if include and include not in selections:
                selections.append(include)
            if exclude and exclude in selections:
                selections.remove(exclude)
            if selections:
                csv = ','.join(selections)
                return changelist.get_query_string({self.parameter_name: csv})
            else:
                return changelist.get_query_string(remove=[self.parameter_name])

        yield {
            'selected': self.value() is None,
            'query_string': changelist.get_query_string(remove=[self.parameter_name]),
            'display': _('All'),
            'reset': True,
        }
        for lookup, title in self.lookup_choices:
            yield {
                'selected': str(lookup) in self.value_as_list(),
                'query_string': changelist.get_query_string({self.parameter_name: lookup}),
                'include_query_string': amend_query_string(include=str(lookup)),
                'exclude_query_string': amend_query_string(exclude=str(lookup)),
                'display': title,
            }


class ProvinciaReligiosaAdmin(ImportMixin, BaseTipoAdmin):
    list_display = (
        'nome', 'descrizione', 'codice',
    )
    search_fields = ('nome', 'descrizione', 'codice')

    resource_class = ProvinciaReligiosaResource


site.register(ProvinciaReligiosa, ProvinciaReligiosaAdmin)


# ##############################            ###################################
# ############################## CURRICULUM ###################################
# ##############################            ###################################


class VoceCurriculumAdmin(PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'ordinamento', 'nome', 'descrizione', 'tipologia_percorso',
    )
    list_filter = ('tipologia_percorso', )
    search_fields = ('nome', 'descrizione')


site.register(VoceCurriculum, VoceCurriculumAdmin)


class CurriculumAdmin(ImportMixin, PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'get_casa_attuale', 'data_curriculum', 'voce_curriculum', 'luogo', 'provincia', 'regione',
        'nazione', 'diocesi',
    )
    date_hierarchy = 'data_curriculum'
    search_fields = ('luogo', 'diocesi', 'nazione__descrizione')
    form = forms.CurriculumForm
    resource_class = CurriculumResource
    autocomplete_fields = ('provincia', 'religioso', 'nazione', 'voce_curriculum')
    list_filter = (DateRangeFilter, 'voce_curriculum')
    link_to_religioso = get_related_field('link_to_religioso', short_description=_('rel.'))
    readonly_fields = ('link_to_religioso', )
    fieldsets = (
        (
            _('Dati Principali'), dict(
                classes=('suit-tab suit-tab-dati_principali', ),
                fields=(
                    'link_to_religioso',
                    'religioso',
                    'data_curriculum',
                    'data_testuale',
                    'voce_curriculum',
                    'luogo',
                    'provincia',
                    'regione', 'nazione',
                    'diocesi',
                    'ordinante',
                    'formatrice',
                    'note',
                )
            )
        ),
    )

    def get_import_resource_class(self):
        return CurriculumImportResource

    def get_queryset(self, request):
        qs = super(CurriculumAdmin, self).get_queryset(request).select_related(
            'religioso', 'religioso__casa_attuale', 'voce_curriculum', 'provincia', 'nazione'
        )
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]
            if data_inizio:
                qs = qs.filter(data_curriculum__gte=data_inizio)
            if data_fine:
                qs = qs.filter(data_curriculum__lte=data_fine)
        return qs


site.register(Curriculum, CurriculumAdmin)


class CurriculumInline(InlineEditLinkMixin, admin.TabularInline):
    model = Curriculum
    fields = (
        'data_curriculum', 'voce_curriculum', 'luogo', 'provincia',
        'link_modifica',
    )
    autocomplete_fields = ('provincia', 'voce_curriculum')
    form = forms.CurriculumForm
    extra = 1
    suit_classes = 'suit-tab suit-tab-curriculum'
    list_select_related = ('provincia', 'voce_curriculum')

    def get_queryset(self, request):
        return super(CurriculumInline, self).get_queryset(request).select_related('provincia', 'voce_curriculum')


# ##############################           ###################################
# ############################## SERVIZIO ####################################
# ##############################           ###################################


class CasaServizioMultipleListFilter(MagisterMultipleListFilter):
    title = _('casa servizio')
    parameter_name = 'presso__in'

    def lookups(self, request, model_admin):
        elenco_case_filtro = []
        pk_qs = model_admin.get_queryset(request).values('presso').distinct()
        elenco_case = CasaPublius.objects.filter(pk__in=pk_qs)
        for casa in elenco_case:
            elenco_case_filtro.append((casa.id, '%s' % casa))
        return elenco_case_filtro


class ServizioAdmin(ImportMixin, PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'get_casa_attuale', 'data_inizio', 'data_fine', 'tipo_servizio', 'get_casa_servizio', 'get_note_display',
    )
    date_hierarchy = 'data_inizio'
    search_fields = (
        'religioso__cognome', 'religioso__nome', 'presso__nome', 'presso__descrizione',
        'specifica', 'dettaglio'
    )
    resource_class = ServizioResource
    autocomplete_fields = ('religioso', 'presso', 'tipo_servizio')
    form = forms.ServizioForm
    list_filter = (
        'tipo_servizio',
        CasaServizioMultipleListFilter,
        ('religioso__casa_attuale', admin.RelatedOnlyFieldListFilter),
    )

    def get_queryset(self, request):
        return super(ServizioAdmin, self).get_queryset(request).select_related(
            'religioso', 'religioso__casa_attuale', 'tipo_servizio', 'presso'
        )

    def get_import_resource_class(self):
        return ServizioImportResource


site.register(Servizio, ServizioAdmin)


class ServizioInline(InlineEditLinkMixin, admin.TabularInline):
    model = Servizio
    fields = (
        'data_inizio', 'data_fine', 'tipo_servizio', 'specifica', 'presso', 'get_note_display',
        'link_modifica',
    )
    autocomplete_fields = ('presso', 'tipo_servizio')
    readonly_fields = ('get_note_display', 'link_modifica')
    form = forms.ServizioForm
    extra = 1
    suit_classes = 'suit-tab suit-tab-servizi'
    list_select_related = ('presso', 'tipo_servizio')

    def get_queryset(self, request):
        return super(ServizioInline, self).get_queryset(request).select_related('presso', 'tipo_servizio')


# ##############################           ###################################
# ############################## DOCUMENTO ###################################
# ##############################           ###################################


class DocumentoAdmin(ImportMixin, PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'get_casa_attuale', 'tipo_documento', 'data_rilascio', 'data_scadenza',
        'numero_documento',
    )
    date_hierarchy = 'data_rilascio'
    search_fields = (
        'religioso__cognome', 'religioso__nome', 'numero_documento',
        'luogo_emissione',
    )
    autocomplete_fields = ('religioso', 'tipo_documento')
    form = forms.DocumentoForm
    resource_class = DocumentoResource
    list_select_related = True
    list_filter = ('tipo_documento', )
    link_to_religioso = get_related_field('link_to_religioso', short_description=_('rel.'))
    readonly_fields = ('link_to_religioso', )
    suit_form_tabs = (
        ('dati_principali', _('Dati Principali')),
        ('allegati', _('Allegati')),
        ('scadenze', _('Scadenze')),
    )
    fieldsets = (
        (
            _('Dati Principali'), dict(
                classes=('suit-tab suit-tab-dati_principali', ),
                fields=(
                    'link_to_religioso',
                    'religioso',
                    'tipo_documento',
                    'data_rilascio',
                    'data_scadenza',
                    'numero_documento',
                    'emittente',
                    'luogo_emissione',
                    'nazione_emissione',
                    'note',
                )
            )
        ),
    )
    inlines = [
        DocumentoAllegatoInlinesAddOnly, DocumentoAllegatoInlinesNoAdd,
        ScadenzaPubliusInline
    ]


site.register(Documento, DocumentoAdmin)


class DocumentoInline(InlineEditLinkMixin, admin.TabularInline):
    model = Documento
    fields = (
        'tipo_documento', 'numero_documento', 'data_rilascio', 'data_scadenza',
        'link_modifica',
    )
    autocomplete_fields = ('tipo_documento', )
    form = forms.DocumentoForm
    extra = 1
    suit_classes = 'suit-tab suit-tab-documenti'

    def get_queryset(self, request):
        return super(DocumentoInline, self).get_queryset(request).select_related('tipo_documento')


# ##############################           ###################################
# ############################## STUDI     ###################################
# ##############################           ###################################


class StudioAdmin(ImportMixin, PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'get_casa_attuale', 'tipo_studio', 'descrizione', 'data_studio', 'qualifica', 'istituto'
    )
    date_hierarchy = 'data_studio'
    search_fields = (
        'religioso__cognome', 'religioso__nome', 'qualifica',
        'istituto', 'descrizione', 'luogo',
    )
    resource_class = StudioExportResource
    autocomplete_fields = ('religioso', 'provincia', 'nazione')
    form = forms.StudioForm
    list_select_related = ('tipo_studio', 'religioso', 'provincia', 'nazione')
    list_filter = ('tipo_studio', )
    link_to_religioso = get_related_field('link_to_religioso', short_description=_('rel.'))
    readonly_fields = ('link_to_religioso', )
    suit_form_tabs = (
        ('dati_principali', _('Dati Principali')),
        ('allegati', _('Allegati')),
    )
    fieldsets = (
        (
            _('Dati Principali'), dict(
                classes=('suit-tab suit-tab-dati_principali', ),
                fields=(
                    'link_to_religioso',
                    'religioso',
                    'tipo_studio',
                    'data_studio',
                    'data_testuale',
                    'descrizione',
                    'qualifica',
                    'istituto',
                    'formatore',
                    'luogo',
                    'nazione',
                    'provincia',
                    'regione',
                    'note',
                )
            )
        ),
    )
    inlines = [
        DocumentoAllegatoInlinesAddOnly, DocumentoAllegatoInlinesNoAdd
    ]

    def get_queryset(self, request):
        return super(StudioAdmin, self).get_queryset(request).select_related(
            'tipo_studio', 'religioso', 'provincia', 'nazione'
        )

    def get_import_resource_class(self):
        return StudioResource


site.register(Studio, StudioAdmin)


class StudioInline(InlineEditLinkMixin, admin.TabularInline):
    model = Studio
    fields = (
        'tipo_studio', 'data_studio', 'qualifica', 'istituto', 'link_modifica',
    )
    autocomplete_fields = ('tipo_studio', )
    form = forms.StudioForm
    extra = 1
    suit_classes = 'suit-tab suit-tab-studi'
    list_select_related = ('tipo_studio', )

    def get_queryset(self, request):
        return super(StudioInline, self).get_queryset(request).select_related('tipo_studio')


# ##############################               ################################
# ############################## PUBBLICAZIONI ################################
# ##############################               ################################


class CategoriaPubblicazioneAdmin(PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'nome', 'codice', 'descrizione',
    )
    search_fields = ('nome', 'codice', 'descrizione')


site.register(CategoriaPubblicazione, CategoriaPubblicazioneAdmin)


class PubblicazioneAdmin(ImportMixin, PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'get_casa_attuale', 'data_pubblicazione', 'oggetto', 'titolo',
        'editore', 'categoria_pubblicazione',
    )
    date_hierarchy = 'data_pubblicazione'
    search_fields = (
        'religioso__cognome', 'religioso__nome', 'oggetto',
        'titolo', 'editore', 'note',
    )
    list_filter = ('categoria_pubblicazione', )
    link_to_religioso = get_related_field('link_to_religioso', short_description=_('rel.'))
    readonly_fields = ('link_to_religioso', )
    autocomplete_fields = ('religioso', )
    resource_class = PubblicazioneResource
    form = forms.PubblicazioneForm
    list_select_related = True
    suit_form_tabs = (
        ('dati_principali', _('Dati Principali')),
        ('allegati', _('Allegati')),
    )
    fieldsets = (
        (
            _('Dati Principali'), dict(
                classes=('suit-tab suit-tab-dati_principali', ),
                fields=(
                    ('religioso', 'link_to_religioso'),
                    ('data_pubblicazione', 'anno_pubblicazione'),
                    ('oggetto', 'argomento'),
                    'titolo',
                    'editore',
                    'categoria_pubblicazione',
                    'tipo_pubblicazione',
                    'tipo_lavoro',
                    'luogo',
                    'lingua',
                    'note',
                )
            )
        ),
    )
    inlines = [
        DocumentoAllegatoInlinesAddOnly, DocumentoAllegatoInlinesNoAdd
    ]


site.register(Pubblicazione, PubblicazioneAdmin)


class PubblicazioneInline(InlineEditLinkMixin, admin.TabularInline):
    model = Pubblicazione
    fields = (
        'data_pubblicazione', 'oggetto', 'titolo', 'categoria_pubblicazione',
        'link_modifica',
    )
    form = forms.PubblicazioneForm
    extra = 1
    suit_classes = 'suit-tab suit-tab-pubblicazioni'


# ##############################               ################################
# ##############################   FAMILIARI   ################################
# ##############################               ################################


class FamiliareAdmin(ImportMixin, PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'grado_parentela', 'cognome', 'nome', 'capofamiglia',
        'indirizzo_residenza', 'luogo_residenza', 'get_casa_attuale', 'contatto'
    )
    list_filter = ('grado_parentela', 'capofamiglia', 'contatto')
    link_to_religioso = get_related_field('link_to_religioso', short_description=_('rel.'))
    readonly_fields = ('link_to_religioso', )
    search_fields = (
        'religioso__cognome', 'religioso__nome', 'cognome', 'nome', 'indirizzo_residenza'
    )
    autocomplete_fields = (
        'religioso', 'nazione_residenza', 'provincia_residenza'
    )
    resource_class = FamiliareResource
    form = forms.FamiliareForm

    def get_queryset(self, request):
        return super(FamiliareAdmin, self).get_queryset(request).select_related(
            'grado_parentela', 'religioso', 'religioso__casa_attuale'
        )


site.register(Familiare, FamiliareAdmin)


class FamiliareInline(InlineEditLinkMixin, admin.TabularInline):
    model = Familiare
    fields = (
        'grado_parentela', 'cognome', 'nome', 'capofamiglia', 'contatto',
        'link_modifica',
    )
    form = forms.FamiliareForm
    autocomplete_fields = ('grado_parentela', )
    extra = 1
    suit_classes = 'suit-tab suit-tab-familiari'

    def get_queryset(self, request):
        return super(FamiliareInline, self).get_queryset(request).select_related('grado_parentela')


# ##############################               ################################
# ##############################   ASSENZE     ################################
# ##############################               ################################


class AssenzaAdmin(ImportMixin, PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'get_casa_attuale', 'data_inizio', 'data_fine', 'descrizione', 'motivo', 'presso'
    )
    search_fields = (
        'religioso__cognome', 'religioso__nome', 'descrizione', 'motivo', 'presso'
    )
    resource_class = AssenzaResource
    autocomplete_fields = (
        'religioso',
    )
    form = forms.AssenzaForm
    list_select_related = True


site.register(Assenza, AssenzaAdmin)


class AssenzaInline(InlineEditLinkMixin, admin.TabularInline):
    model = Assenza
    fields = (
        'data_inizio', 'data_fine', 'descrizione', 'motivo', 'presso', 'link_modifica'
    )
    form = forms.AssenzaForm
    extra = 1
    suit_classes = 'suit-tab suit-tab-assenze'


# ##############################               ################################
# ##############################   RICOVERI     ################################
# ##############################               ################################


class RicoveroAdmin(PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'get_casa_attuale', 'data_inizio', 'data_fine', 'presunta_patologia', 'luogo',
    )
    search_fields = (
        'religioso__cognome', 'religioso__nome', 'presunta_patologia', 'luogo', 'note'
    )
    date_hierarchy = 'data_inizio'
    list_filter = (
        ('religioso__casa_attuale', RelatedOnlyFieldListFilter),
        'data_fine'
    )
    autocomplete_fields = (
        'religioso',
    )
    form = forms.RicoveroForm
    list_select_related = True


site.register(Ricovero, RicoveroAdmin)


class RicoveroInline(InlineEditLinkMixin, admin.TabularInline):
    model = Ricovero
    fields = (
        'data_inizio', 'data_fine', 'presunta_patologia', 'luogo', 'link_modifica'
    )
    form = forms.RicoveroForm
    extra = 1
    suit_classes = 'suit-tab suit-tab-ricoveri'


# ##############################               ################################
# ##############################   VIAGGI      ################################
# ##############################               ################################


class ViaggioAdmin(PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'get_casa_attuale', 'partenza', 'arrivo', 'data_inizio', 'data_fine',
        'nazione', 'motivo',
    )
    search_fields = (
        'religioso__cognome', 'religioso__nome', 'partenza', 'arrivo', 'motivo',
    )
    autocomplete_fields = (
        'religioso', 'nazione',
    )
    form = forms.ViaggioForm
    list_select_related = True


site.register(Viaggio, ViaggioAdmin)


class ViaggioInline(InlineEditLinkMixin, admin.TabularInline):
    model = Viaggio
    fields = (
        'partenza', 'arrivo', 'data_inizio', 'data_fine',
        'nazione', 'link_modifica'
    )
    list_select_related = ('nazione', )
    form = forms.ViaggioForm
    autocomplete_fields = ('nazione', )
    extra = 1
    suit_classes = 'suit-tab suit-tab-viaggi'

    def get_queryset(self, request):
        return super(ViaggioInline, self).get_queryset(request).select_related('nazione')


# ##############################               ################################
# ##############################   LINGUE      ################################
# ##############################               ################################


class LinguaConosciutaAdmin(PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'get_casa_attuale', 'lingua', 'livello', 'scritto', 'parlato', 'compreso',
    )
    search_fields = (
        'religioso__cognome', 'religioso__nome',
    )
    autocomplete_fields = ('religioso', 'lingua')
    list_filter = ('scritto', 'parlato', 'compreso', )
    list_select_related = True


site.register(LinguaConosciuta, LinguaConosciutaAdmin)


class LinguaConosciutaInline(InlineEditLinkMixin, admin.TabularInline):
    model = LinguaConosciuta
    fields = (
        'lingua', 'livello', 'scritto', 'parlato', 'compreso', 'link_modifica',
    )
    autocomplete_fields = ('lingua', )
    extra = 1
    suit_classes = 'suit-tab suit-tab-lingue'

    def get_queryset(self, request):
        return super(LinguaConosciutaInline, self).get_queryset(request).select_related('lingua')


# ##############################                    ###########################
# ############################## GRUPPI/COMMISSIONI ###########################
# ##############################                    ###########################

class TipoCommissioneAdmin(PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'codice', 'nome', 'descrizione',
    )
    search_fields = ('nome', 'descrizione')


site.register(TipoCommissione, TipoCommissioneAdmin)


class GruppoCommissioneAdmin(ImportMixin, PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'get_casa_attuale', 'data_inizio', 'data_fine', 'gruppo_commissione', 'ruolo'
    )
    search_fields = (
        'religioso__cognome', 'religioso__nome', 'gruppo_commissione', 'ruolo',
        'note',
    )
    resource_class = GruppoCommissioneResource
    form = forms.GruppoCommissioneForm
    autocomplete_fields = ('religioso', 'provincia_religiosa')
    list_select_related = True


site.register(GruppoCommissione, GruppoCommissioneAdmin)


class GruppoCommissioneInline(InlineEditLinkMixin, admin.TabularInline):
    model = GruppoCommissione
    fields = (
        'data_inizio', 'data_fine', 'gruppo_commissione', 'ruolo', 'link_modifica',
    )
    form = forms.GruppoCommissioneForm
    extra = 1
    suit_classes = 'suit-tab suit-tab-gruppi'


# ##############################                     ###########################
# ############################## RIPRESA VOCAZIONALE ###########################
# ##############################                     ###########################

class TipoRipresaAdmin(PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'codice', 'nome', 'descrizione',
    )
    search_fields = ('nome', 'descrizione', 'codice')


site.register(TipoRipresa, TipoRipresaAdmin)


class RipresaVocazionaleAdmin(ImportMixin, PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'get_casa_attuale', 'tipo_ripresa', 'descrizione_corso', 'durata', 'anno', 'citta_ripresa'
    )
    list_filter = ('tipo_ripresa', 'anno')
    search_fields = (
        'religioso__cognome', 'religioso__nome', 'descrizione_corso', 'durata',
        'note',
    )
    resource_class = RipresaVocazionaleResource
    form = forms.RipresaVocazionaleForm
    autocomplete_fields = ('religioso', 'nazione_ripresa')
    list_select_related = True


site.register(RipresaVocazionale, RipresaVocazionaleAdmin)


class RipresaVocazionaleInline(InlineEditLinkMixin, admin.TabularInline):
    model = RipresaVocazionale
    fields = (
        'tipo_ripresa', 'descrizione_corso', 'durata', 'anno', 'citta_ripresa', 'link_modifica',
    )
    form = forms.RipresaVocazionaleForm
    extra = 1
    suit_classes = 'suit-tab suit-tab-ripresa_vocazionale'


# ##############################                    ###########################
# ############################## ASSEGNAZIONI       ###########################
# ##############################                    ###########################


class AssegnazioneAdmin(ImportMixin, PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'nazione', 'circoscrizione_assegnazione',
        'lettera_assegnazione', 'trasferimento_effettivo',
    )
    search_fields = (
        'religioso__cognome', 'religioso__nome', 'note',
        'nazione__descrizione', 'circoscrizione_assegnazione__descrizione'
    )
    form = forms.AssegnazioneForm
    resource_class = AssegnazioneResource
    autocomplete_fields = (
        'religioso', 'nazione', 'circoscrizione_assegnazione',
        'assegnazione_precedente'
    )
    list_select_related = True


site.register(Assegnazione, AssegnazioneAdmin)


class AssegnazioneInline(InlineEditLinkMixin, admin.TabularInline):
    model = Assegnazione
    fields = (
        'religioso', 'nazione', 'circoscrizione_assegnazione',
        'lettera_assegnazione', 'assegnazione_precedente', 'link_modifica',
    )
    form = forms.AssegnazioneForm
    extra = 1
    autocomplete_fields = (
        'religioso', 'nazione', 'circoscrizione_assegnazione',
        'assegnazione_precedente'
    )
    suit_classes = 'suit-tab suit-tab-assegnazioni'
    list_select_related = (
        'nazione', 'circoscrizione_assegnazione', 'assegnazione_precedente',
    )

    def get_queryset(self, request):
        return super(AssegnazioneInline, self).get_queryset(request).select_related(
            'nazione', 'circoscrizione_assegnazione', 'assegnazione_precedente'
        )


# ##############################                         ######################
# ############################## CONSIGLI CIRCOSCRIZIONE ######################
# ##############################                         ######################


class ConsiglioCircoscrizioneAdmin(ImportMixin, PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'provincia_religiosa', 'carica', 'periodo_incarico',
        'tipo_carica', 'membro_attuale',
    )
    search_fields = (
        'religioso__cognome', 'religioso__nome', 'note', 'promemoria',
        'provincia_religiosa__descrizione', 'anno_inizio_incarico',
        'periodo_incarico'
    )
    list_filter = (
        'membro_attuale', 'carica',
        ('provincia_religiosa', RelatedOnlyFieldListFilter),
    )
    form = forms.ConsiglioCircoscrizioneForm
    resource_class = ConsiglioCircoscrizioneResource
    autocomplete_fields = (
        'religioso', 'provincia_religiosa',
    )
    list_select_related = ('religioso', 'provincia_religiosa')


site.register(ConsiglioCircoscrizione, ConsiglioCircoscrizioneAdmin)


class ConsiglioCircoscrizioneInline(InlineEditLinkMixin, admin.TabularInline):
    model = ConsiglioCircoscrizione
    fields = (
        'religioso', 'provincia_religiosa', 'carica', 'carica_secondaria',
        'anno_inizio_incarico', 'membro_attuale', 'link_modifica',
    )
    form = forms.ConsiglioCircoscrizioneForm
    extra = 1
    autocomplete_fields = ('religioso', 'provincia_religiosa', )
    suit_classes = 'suit-tab suit-tab-consigli_circoscrizione'
    list_select_related = ('provincia_religiosa', )

    def get_queryset(self, request):
        return super(ConsiglioCircoscrizioneInline, self).get_queryset(request).select_related(
            'provincia_religiosa',
        )


# ##############################                         ######################
# ############################## CONSIGLI GENERALI       ######################
# ##############################                         ######################


class ConsiglioGeneraleAdmin(ImportMixin, PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'carica', 'ufficio', 'anno_inizio_incarico', 'periodo_incarico',
        'membro_attuale',
    )
    search_fields = (
        'religioso__cognome', 'religioso__nome', 'note',
        'anno_inizio_incarico', 'periodo_incarico'
    )
    list_filter = ('membro_attuale', 'carica', 'codice_carica')
    form = forms.ConsiglioGeneraleForm
    resource_class = ConsiglioGeneraleResource
    autocomplete_fields = ('religioso', )
    list_select_related = True


site.register(ConsiglioGenerale, ConsiglioGeneraleAdmin)


class ConsiglioGeneraleInline(InlineEditLinkMixin, admin.TabularInline):
    model = ConsiglioGenerale
    fields = (
        'religioso', 'carica', 'ufficio', 'anno_inizio_incarico',
        'membro_attuale', 'link_modifica',
    )
    form = forms.ConsiglioGeneraleForm
    extra = 1
    autocomplete_fields = ('religioso', )
    suit_classes = 'suit-tab suit-tab-consigli_generali'


# ##############################                    ###########################
# ############################## ASSEMBLEE          ###########################
# ##############################                    ###########################


class AssembleaAdmin(ImportMixin, PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'data_inizio', 'provincia_religiosa',
        'descrizione', 'luogo_e_data',
    )
    search_fields = (
        'religioso__cognome', 'religioso__nome', 'note',
        'provincia_religiosa__descrizione', 'descrizione'
    )
    form = forms.AssembleaForm
    resource_class = AssembleaResource
    autocomplete_fields = (
        'religioso', 'provincia_religiosa',
    )
    list_select_related = True


site.register(Assemblea, AssembleaAdmin)


class AssembleaInline(InlineEditLinkMixin, admin.TabularInline):
    model = Assemblea
    fields = (
        'religioso', 'data_inizio', 'provincia_religiosa',
        'descrizione', 'luogo_e_data', 'link_modifica',
    )
    form = forms.AssembleaForm
    extra = 1
    autocomplete_fields = ('religioso', 'provincia_religiosa', )
    suit_classes = 'suit-tab suit-tab-assemblee'
    list_select_related = ('provincia_religiosa', )

    def get_queryset(self, request):
        return super(AssembleaInline, self).get_queryset(request).select_related(
            'provincia_religiosa',
        )

# ##############################                     ###########################
# ############################## PROFILI / BIOGRAFIE ###########################
# ##############################                     ###########################


class ProfiloBiografiaAdmin(ImportMixin, PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'descrizione', 'anno',
    )
    search_fields = (
        'religioso__cognome', 'religioso__nome', 'descrizione', 'anno'
    )
    form = forms.ProfiloBiografiaForm
    resource_class = ProfiloBiografiaResource
    autocomplete_fields = (
        'religioso',
    )


site.register(ProfiloBiografia, ProfiloBiografiaAdmin)


class ProfiloBiografiaInline(InlineEditLinkMixin, admin.TabularInline):
    model = ProfiloBiografia
    fields = (
        'religioso', 'descrizione', 'anno', 'link_modifica',
    )
    form = forms.ProfiloBiografiaForm
    extra = 1
    autocomplete_fields = ('religioso', )
    suit_classes = 'suit-tab suit-tab-profilibiografie'


# ##############################             ###########################
# ############################## BENEMERENZE ###########################
# ##############################             ###########################


class BenemerenzaAdmin(ImportMixin, PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'descrizione', 'anno',
    )
    search_fields = (
        'religioso__cognome', 'religioso__nome', 'descrizione', 'anno'
    )
    form = forms.BenemerenzaForm
    resource_class = BenemerenzaResource
    autocomplete_fields = (
        'religioso',
    )


site.register(Benemerenza, BenemerenzaAdmin)


class BenemerenzaInline(InlineEditLinkMixin, admin.TabularInline):
    model = Benemerenza
    fields = (
        'religioso', 'descrizione', 'anno', 'link_modifica',
    )
    form = forms.BenemerenzaForm
    extra = 1
    autocomplete_fields = ('religioso', )
    suit_classes = 'suit-tab suit-tab-benemerenze'

# ##############################                    ###########################
# ##############################    TRASFERIMENTI   ###########################
# ##############################                    ###########################


class ReligiosoStatoUscitaListFilter(SimpleListFilter):
    title = _('Stato Uscita')
    parameter_name = 'uscita'

    def value_as_list(self):
        return self.value().split(',') if self.value() else []

    def lookups(self, request, model_admin):
        if settings.CONGREGAZIONE_FEMMINILE:
            return (
                ('presenti', _('Solo Presenti')),
                ('usciti', _('Solo Uscite')),
            )
        else:
            return (
                ('presenti', _('Solo Presenti')),
                ('usciti', _('Solo Usciti')),
            )

    def queryset(self, request, queryset):
        if self.value() is not None:
            if self.value() == 'presenti':
                return queryset.filter(religioso__data_uscita__isnull=True)
            if self.value() == 'usciti':
                return queryset.filter(religioso__data_uscita__isnull=False)
        else:
            return queryset


class TrasferimentoAdmin(ImportMixin, PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'get_casa_attuale', 'data_trasferimento', 'da_provincia', 'a_provincia', 'motivo', 'data_fine', 'get_note_display',
    )
    date_hierarchy = 'data_trasferimento'
    search_fields = (
        'religioso__cognome', 'religioso__nome', 'motivo',
    )
    list_filter = (
        'a_provincia',
        'religioso__in_vita',
        ReligiosoStatoUscitaListFilter,
        'data_fine',
    )
    suit_list_filter_horizontal = ('a_provincia', )
    resource_class = TrasferimentoResource
    form = forms.TrasferimentoForm
    autocomplete_fields = ('religioso', 'da_provincia', 'a_provincia')
    list_select_related = ('da_provincia', 'a_provincia')

    def get_import_resource_class(self):
        return TrasferimentoImportResource


site.register(Trasferimento, TrasferimentoAdmin)


class TrasferimentoInline(InlineEditLinkMixin, admin.TabularInline):
    model = Trasferimento
    fields = (
        'data_trasferimento', 'data_fine', 'a_provincia', 'motivo', 'get_note_display',
        'link_modifica',
    )
    form = forms.TrasferimentoForm
    readonly_fields = ('get_note_display', 'link_modifica')
    autocomplete_fields = ('da_provincia', 'a_provincia')
    extra = 1
    suit_classes = 'suit-tab suit-tab-trasferimenti'
    list_select_related = ('da_provincia', 'a_provincia')

    def get_queryset(self, request):
        return super(TrasferimentoInline, self).get_queryset(request).select_related(
            'da_provincia', 'a_provincia',
        )


# ##############################                    ###########################
# ##############################    RATEI PENSIONI        ###########################
# ##############################                    ###########################


class RateoPensioneAdmin(PubliusModelAdmin, RelatedFieldAdmin):
    list_display = (
        'religioso', 'get_casa_attuale', 'data_rateo', 'importo', 'descrizione', 'tipo_pensione',
        'get_importo_incassato_display', 'modalita_incasso',
    )
    search_fields = (
        'religioso__cognome', 'religioso__nome', 'descrizione',
    )
    date_hierarchy = 'data_rateo'
    list_filter = (DateRangeFilter, 'tipo_pensione', 'modalita_incasso')
    resource_class = RateoPensioneResource
    form = forms.RateoPensioneForm
    autocomplete_fields = ('religioso', )
    list_select_related = True

    def suit_cell_attributes(self, obj, column):
        if column in [
            'importo', 'get_importo_incassato_display'
        ]:
            return {'class': 'text-xs-right'}
       
    def get_queryset(self, request):
        qs = super(RateoPensioneAdmin, self).get_queryset(request).select_related(
            'religioso', 'religioso__casa_attuale', 'tipo_pensione', 'modalita_incasso'
        )
        data_inizio = None
        data_fine = None
        params = dict(request.GET.items()).copy()
        range_filter = DateRangeFilter(request, params=params, model=self.model, model_admin=self)
        if range_filter.form.is_valid():
            data_inizio = range_filter.form.cleaned_data[range_filter.lookup_kwarg_gte]
            data_fine = range_filter.form.cleaned_data[range_filter.lookup_kwarg_lte]
            if data_inizio:
                qs = qs.filter(data_rateo__gte=data_inizio)
            if data_fine:
                qs = qs.filter(data_rateo__lte=data_fine)
        return qs


site.register(RateoPensione, RateoPensioneAdmin)


class RateoPensioneInline(InlineEditLinkMixin, admin.TabularInline):
    model = RateoPensione
    fields = (
        'data_rateo', 'importo', 'descrizione', 'tipo_pensione', 'importo_incassato', 'modalita_incasso', 'link_modifica',
    )
    form = forms.RateoPensioneForm
    extra = 1
    suit_classes = 'suit-tab suit-tab-ratei_pensioni'

    def get_queryset(self, request):
        return super(RateoPensioneInline, self).get_queryset(request).select_related(
            'religioso', 'religioso__casa_attuale', 'tipo_pensione', 'modalita_incasso'
        )


# ##############################           ###################################
# ############################## RELIGIOSO ###################################
# ##############################           ###################################


class NazioneMultipleListFilter(MagisterMultipleListFilter):
    title = _('Nazione Nascita')
    parameter_name = 'nazione_nascita__in'

    def lookups(self, request, model_admin):
        elenco_nazioni = []
        pk_qs = model_admin.get_queryset(request).values('nazione_nascita').distinct()
        elenco_stati = Stato.objects.filter(pk__in=pk_qs)
        for nazione in elenco_stati:
            elenco_nazioni.append((nazione.id, '%s' % nazione))
        return elenco_nazioni


class StatusMultipleListFilter(MagisterMultipleListFilter):
    title = _('Status')
    parameter_name = 'status__in'

    def lookups(self, request, model_admin):
        elenco_status_filtro = []
        pk_qs = model_admin.get_queryset(request).values('status').distinct()
        elenco_status = Status.objects.filter(pk__in=pk_qs)
        for status in elenco_status:
            elenco_status_filtro.append((status.id, '%s' % status))
        return elenco_status_filtro


class StatusReligiosoMultipleListFilter(MagisterMultipleListFilter):
    title = _('Status Religioso')
    parameter_name = 'status_religioso__in'

    def lookups(self, request, model_admin):
        elenco_status_religioso_filtro = []
        pk_qs = model_admin.get_queryset(request).values('status_religioso').distinct()
        elenco_status_religiosi = VoceCurriculum.objects.filter(pk__in=pk_qs)
        for status in elenco_status_religiosi:
            elenco_status_religioso_filtro.append((status.id, '%s' % status))
        return elenco_status_religioso_filtro


class ProvinciaReligiosaMultipleListFilter(MagisterMultipleListFilter):
    title = _('Provincia Religiosa')
    parameter_name = 'provincia_religiosa__in'

    def lookups(self, request, model_admin):
        elenco_province_filtro = []
        pk_qs = model_admin.get_queryset(request).values('provincia_religiosa').distinct()
        elenco_province_religiose = ProvinciaReligiosa.objects.filter(pk__in=pk_qs)
        for provincia in elenco_province_religiose:
            elenco_province_filtro.append((provincia.id, '%s' % provincia))
        return elenco_province_filtro


class CasaAttualeMultipleListFilter(MagisterMultipleListFilter):
    title = _('Casa')
    parameter_name = 'casa_attuale__in'

    def lookups(self, request, model_admin):
        elenco_case_filtro = []
        pk_qs = model_admin.get_queryset(request).values('casa_attuale').distinct()
        elenco_case = CasaPublius.objects.filter(pk__in=pk_qs)
        for casa in elenco_case:
            elenco_case_filtro.append((casa.id, '%s' % casa))
        return elenco_case_filtro


class StatoUscitaListFilter(SimpleListFilter):
    title = _('Stato Uscita')
    parameter_name = 'uscita'

    def value_as_list(self):
        return self.value().split(',') if self.value() else []

    def lookups(self, request, model_admin):
        if settings.CONGREGAZIONE_FEMMINILE:
            return (
                ('presenti', _('Solo Presenti')),
                ('usciti', _('Solo Uscite')),
            )
        else:
            return (
                ('presenti', _('Solo Presenti')),
                ('usciti', _('Solo Usciti')),
            )

    def queryset(self, request, queryset):
        if self.value() is not None:
            if self.value() == 'presenti':
                return queryset.filter(data_uscita__isnull=True)
            if self.value() == 'usciti':
                return queryset.filter(data_uscita__isnull=False)
        else:
            return queryset


class ReligiosoAdmin(ImportMixin, PubliusModelAdmin):
    list_display = (
        'cognome', 'nome', 'appellativo', 'data_nascita', 'casa_attuale',
        'status', 'provincia_religiosa', 'status_religioso', 'nazione_nascita',
        'in_vita',
    )
    list_filter = (
        'in_vita',
        StatoUscitaListFilter,
        CasaAttualeMultipleListFilter,
        # ('casa_attuale', RelatedOnlyFieldListFilter),
        # ('status', RelatedOnlyFieldListFilter),
        # ('gruppo_appartenenza', RelatedOnlyFieldListFilter),
        # ('provincia_religiosa', RelatedOnlyFieldListFilter),
        StatusMultipleListFilter,
        ProvinciaReligiosaMultipleListFilter,
        NazioneMultipleListFilter,
        StatusReligiosoMultipleListFilter,
        ('posizione_canonica', RelatedOnlyFieldListFilter),
        'provincia_nascita', 
        'provincia_residenza', 
        'nazione_residenza',
        'elettorato',
        ('incarico', RelatedOnlyFieldListFilter),
    )
    suit_list_filter_horizontal = (
        'provincia_nascita', 'provincia_residenza', 'nazione_residenza',
    )
    # suit_list_filter_horizontal = (
    #     'casa_attuale', 'provincia_religiosa', 'gruppo_appartenenza',
    # )
    readonly_fields = [
        'link_to_casa', 'fotografia_anteprima', 'fotografia_anteprima_piccola',
        'status_religioso', 'in_vita',
    ]
    autocomplete_fields = (
        'nazione_residenza', 'nazione_nascita', 'provincia_residenza',
        'nazione_morte', 'provincia_nascita', 'provincia_morte',
        'casa_attuale', 'cittadinanza', 'nazionalita', 'comunita_morte',
        'nazione_battesimo', 'utente', 'incarico', 'tipo_domicilio',
    )
    list_select_related = [
        'casa_attuale', 'status', 'provincia_religiosa', 'status_religioso',
        'nazione_nascita', 'nazione_residenza', 'nazione_morte',
        'cittadinanza', 'nazionalita', 'nazione_battesimo', 'provincia_morte',
        'comunita_morte',
    ]
    actions = (
        'stampa_catalogo_generale', 'stampa_elenco_semplice',
        'stampa_elenco_indirizzi', 'stampa_elenco_date',
        'stampa_elenco_parenti', 'stampa_elenco_curriculum',
        'stampa_elenco_servizi', 'stampa_elenco_studi',
        'stampa_elenco_compleanni', 'stampa_rubrica_telefonica',
        'stampa_elenco_defunti', 'stampa_ricorrenze_defunti',
        'stampa_schede_complete', 'stampa_dati_sanitari',
        'vedi_grafico_status_religioso', 'vedi_grafico_eta_religiosi',
        'vedi_grafico_case_religiosi', 'vedi_grafico_status',
        'vedi_grafico_provincia_religiosa', 'crea_utenti_ego',
    )
    form = forms.ReligiosoForm
    search_fields = (
        'cognome', 'nome', 'appellativo', 'casa_attuale__nome',
        'casa_attuale__descrizione', 'codice_fiscale', 'annotazioni',
        'luogo_nascita', 'patente', 'attitudini',
    )
    link_to_casa = get_related_field('link_to_casa_attuale', short_description=_('casa'))
    # change_list_template = 'religioso_change_list.html'
    resource_class = ReligiosoResource
    suit_form_tabs = [
        ('dati_principali', _('Dati Principali')),
        ('nascita', _('Nascita')),
        ('residenza', _('Residenza')),
        ('notizie', _('Notizie')),
        ('curriculum', _('Curriculum')),
        ('servizi', _('Servizi')),
        ('assegnazioni', _('Assegnazioni')),
        ('trasferimenti', _('Trasferimenti')),
        ('familiari', _('Familiari')),
        ('studi', _('Studi')),
        ('ripresa_vocazionale', _('Ripr.Vocazionale')),
        ('assenze', _('Assenze')),
        ('ricoveri', _('Ricoveri')),
        ('pubblicazioni', _('Pubblicazioni')),
        ('profilibiografie', _('Profili - Biografie')),
        ('benemerenze', _('Benemerenze')),
        ('documenti', _('Documenti')),
        ('viaggi', _('Viaggi')),
        ('lingue', _('Lingue')),
        ('gruppi', _('Gruppi/Commissioni')),
        ('assemblee', _('Assemblee/Incontri')),
        ('consigli_circoscrizione', _('Cons. Circ.')),
        ('consigli_generali', _('Cons. Generale')),
        ('fotografia', _('Fotografia')),
        ('allegati', _('Allegati')),
        ('scadenze', _('Scadenze')),
        ('salute', _('Salute')),
        ('uscita', _('Uscita')),
        ('decesso', _('Decesso')),
        ('note_riservate', _('Note Riservate')),
    ]
    inlines = [
        CurriculumInline, ServizioInline, DocumentoInline, StudioInline,
        PubblicazioneInline, ProfiloBiografiaInline, BenemerenzaInline,
        FamiliareInline, AssenzaInline, RicoveroInline, ViaggioInline,
        LinguaConosciutaInline, GruppoCommissioneInline, TrasferimentoInline,
        DocumentoAllegatoInlinesAddOnly, AssegnazioneInline, RipresaVocazionaleInline,
        DocumentoAllegatoInlinesNoAdd, ScadenzaPubliusInline, AssembleaInline,
        ConsiglioCircoscrizioneInline, ConsiglioGeneraleInline
    ]
    fieldsets = (
        (
            _(' '), dict(
                classes=('suit-tab suit-tab-dati_principali', ),
                fields=(
                    'fotografia_anteprima_piccola',
                )
            )
        ),
        (
            _(' '), dict(
                fields=(
                    ('appellativo', 'iniziali'),
                    'cognome',
                    'nome',
                )
            )
        ),
        (
            _('Dati principali'), dict(
                classes=('suit-tab suit-tab-dati_principali', ),
                fields=(
                    ('status', 'status_religioso'),
                    'posizione_canonica',
                    ('matricola', 'marca'),
                    ('numero_congregazione', 'numero_camera'),
                    ('casa_attuale', 'link_to_casa'),
                    'incarico',
                    ('gruppo_appartenenza', 'in_vita'),
                    ('utente'),
                    'elettorato',
                )
            )
        ),
        (
            _('Nascita'), dict(
                classes=('suit-tab suit-tab-nascita', ),
                fields=(
                    'data_nascita',
                    'luogo_nascita',
                    'provincia_nascita',
                    'regione_nascita',
                    'nazione_nascita',
                    'diocesi_nascita',
                    'nome_battesimo',
                    'nome_civile',
                    'cittadinanza',
                    'nazionalita',
                    'padre',
                    'madre',
                    'stato_famiglia',
                )
            )
        ),
        (
            _('Battesimo'), dict(
                classes=('suit-tab suit-tab-nascita', ),
                fields=(
                    'luogo_battesimo',
                    'data_battesimo',
                    'diocesi_battesimo',
                    'nazione_battesimo',
                )
            )
        ),
        (
            _('Cresima'), dict(
                classes=('suit-tab suit-tab-nascita', ),
                fields=(
                    'luogo_cresima',
                    'data_cresima',
                    'diocesi_cresima',
                )
            )
        ),
        (
            _('Dati Caratteriali'), dict(
                classes=('suit-tab suit-tab-notizie', ),
                fields=(
                    'carattere',
                    'attitudini',
                    'annotazioni',
                )
            )
        ),
        (
            _('Altri dati'), dict(
                classes=('suit-tab suit-tab-notizie', ),
                fields=(
                    'destinazioni_incarichi',
                    'vescovo_ordinante',
                    'patente',
                )
            )
        ),
        (
            _('Dati Fiscali'), dict(
                classes=('suit-tab suit-tab-notizie', ),
                fields=(
                    'codice_fiscale',
                    'esenzione',
                    'percentuale',
                    'codice_esenzione',
                    'pensione',
                    'status_pensione',
                    'codice_inps',
                )
            )
        ),
        (
            _('Dati medici'), dict(
                classes=('suit-tab suit-tab-salute', ),
                fields=(
                    'medico_curante',
                    'tessera_sanitaria',
                    'gruppo_sanguigno',
                )
            )
        ),
        (
            _('Dati uscita'), dict(
                classes=('suit-tab suit-tab-uscita', ),
                fields=(
                    'data_uscita',
                    'luogo_uscita',
                    'causa_uscita',
                    'numero_uscita',
                    'motivazione_uscita',
                    'note_uscita',
                    'indirizzo_uscita',
                    'notizie_dopo_uscita',
                )
            )
        ),
        (
            _('Note Riservate'), dict(
                classes=('suit-tab suit-tab-note_riservate', ),
                fields=(
                    'note_riservate',
                )
            )
        ),
        (
            _('Dati decesso'), dict(
                classes=('suit-tab suit-tab-decesso', ),
                fields=(
                    'numero_decesso',
                    'data_testamento',
                    'notaio',
                    'data_morte',
                    'ora_morte',
                    'luogo_morte',
                    'provincia_morte',
                    'regione_morte',
                    'nazione_morte',
                    'comunita_morte',
                    'causa_morte',
                    'luogo_sepoltura',
                    'necrologio',
                    'testimonianze',
                    'vita',
                )
            )
        ),
        (
            _('Residenza'), dict(
                classes=('suit-tab suit-tab-residenza', ),
                fields=(
                    'casa_residenza',
                    'indirizzo_residenza',
                    'luogo_residenza',
                    ('provincia_residenza', 'cap_residenza',),
                    'nazione_residenza',
                    'regione_residenza',
                    'diocesi_residenza',
                    'provincia_religiosa',
                    'regione_religiosa',
                    'provincia_giuridica',
                    'data_residenza',
                )
            )
        ),
        (
            _('Domicilio'), dict(
                classes=('suit-tab suit-tab-residenza', ),
                fields=(
                    'indirizzo_domicilio',
                    'luogo_domicilio',
                    'tipo_domicilio',
                    'data_domicilio',
                )
            )
        ),
        (
            _('Contatti'), dict(
                classes=('suit-tab suit-tab-residenza', ),
                fields=(
                    'telefono_1',
                    'telefono_2',
                    'fax',
                    'cellulare',
                    'email',
                    'email_2',
                    'contatti',
                )
            )
        ),
        (
            _('Fotografia'), dict(
                classes=('suit-tab suit-tab-fotografia', ),
                fields=(
                    'fotografia_anteprima',
                    'fotografia',
                )
            )
        ),
    )

    def get_import_resource_class(self):
        return ReligiosoImportResource

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        url_patterns = [
            url(
                r'graficostatusreligiosi/$',
                self.admin_site.admin_view(StatusReligiosoPersoneView.as_view()),
                name='persone_religioso_graficostatusreligiosi',
            ),
            url(
                r'graficostatus/',
                self.admin_site.admin_view(StatusPersoneView.as_view()),
                name='persone_religioso_graficostatus'
            ),
            url(
                r'graficoreligiosicasa/',
                self.admin_site.admin_view(ReligiosiPerCasaView.as_view()),
                name='persone_religioso_graficoreligiosicasa'
            ),
            url(
                r'graficoetareligiosi/',
                self.admin_site.admin_view(FasceEtaReligiosiView.as_view()),
                name='persone_religioso_graficoetareligiosi'
            ),
            url(
                r'graficoprovinciareligiosa/',
                self.admin_site.admin_view(ProvinciaReligiosaView.as_view()),
                name='persone_religioso_graficoprovinciareligiosa'
            ),
            url(
                r'esportastatistiche/',
                self.admin_site.admin_view(self.esporta_statistiche),
                name='%s_%s_esportastatistiche' % info
            ),
            url(
                r'stampaannuariofamiliari/',
                self.admin_site.admin_view(self.stampa_annuario_familiari),
                name='%s_%s_stampaannuariofamiliari' % info
            ),
            url(
                r'stampaannuariodefunti/',
                self.admin_site.admin_view(self.stampa_annuario_defunti),
                name='%s_%s_stampaannuariodefunti' % info
            ),
            url(
                r'stampaannuariocase/',
                self.admin_site.admin_view(self.stampa_annuario_componenti_case),
                name='%s_%s_stampaannuariocase' % info
            ),
            url(
                r'stampaannuariocaseperarea/',
                self.admin_site.admin_view(self.stampa_annuario_componenti_case_per_area),
                name='%s_%s_stampaannuariocaseperarea' % info
            ),
            url(
                r'stampaannuariocasevecchio/',
                self.admin_site.admin_view(self.stampa_annuario_componenti_case_vecchio),
                name='%s_%s_stampaannuariocasevecchio' % info
            ),
            url(
                r'stampaannuarionazionalita/',
                self.admin_site.admin_view(self.stampa_annuario_nazionalita),
                name='%s_%s_stampaannuarionazionalita' % info
            ),
            url(
                r'stampaquadroriassuntivo/',
                self.admin_site.admin_view(self.stampa_quadro_riassuntivo),
                name='%s_%s_stampaquadroriassuntivo' % info
            ),
        ]
        url_patterns += super(ReligiosoAdmin, self).get_urls()
        return url_patterns

    def fotografia_anteprima(self, obj):
        if obj.fotografia:
            return mark_safe('<img src="{}" width="350" />'.format(obj.fotografia.url))
        return ''
    fotografia_anteprima.short_description = _('Anteprima (grande)')
    fotografia_anteprima.allow_tags = True

    def fotografia_anteprima_piccola(self, obj):
        if obj.fotografia:
            return mark_safe('<img src="{}" width="150" />'.format(obj.fotografia.url))
        return ''
    fotografia_anteprima_piccola.short_description = _('Foto')
    fotografia_anteprima_piccola.allow_tags = True

    def esporta_statistiche(self, request, queryset=None):
        elenco_religiosi, aggiunta_titolo = get_elenco_religiosi_filtrato(request)
        if not elenco_religiosi:
            elenco_religiosi = Religioso.objects.all()
        aree_figlie = request.user.area_corrente.get_descendants(include_self=True)
        elenco_religiosi = elenco_religiosi.filter(casa_attuale__area__in=aree_figlie)
        # modifica un modello di file excel con le statistiche dei religiosi calcolate nella dashboard
        # e lo restituisce come file da scaricare
        cartella_base = join(settings.BASE_DIR, 'publius/persone/templates')
        nome_template = 'proiezione_statistiche.xlsx'
        file_template = '%s/%s' % (cartella_base, nome_template)
        workbook = openpyxl.load_workbook(file_template)
        worksheet = workbook.active
        # worksheet.title = 'Statistiche (%s)' % stato_cittadinanza
        percentuali_decessi = get_percentuale_decessi_uscite_fasce(request.user.area_corrente, elenco_religiosi)
        riga_percentuali = 2
        colonna_iniziale = 2
        for percentuale_decesso in percentuali_decessi.items():
            valore_percentuale = percentuale_decesso[1] / 100
            worksheet.cell(riga_percentuali, colonna_iniziale, valore_percentuale)
            colonna_iniziale += 1
        riga_religiosi_attuali = 3
        colonna_iniziale = 2
        data_corrente = date.today()
        fasce_religiosi_oggi = get_religiosi_presenti_alla_data(request.user.area_corrente, data_corrente, elenco_religiosi)
        for fascia_religiosi in fasce_religiosi_oggi.items():
            worksheet.cell(riga_religiosi_attuali, colonna_iniziale, fascia_religiosi[1])
            colonna_iniziale += 1
        response = HttpResponse(content_type='application/vnd.ms-excel')
        nome_file = 'proiezioni_statistiche'
        if aggiunta_titolo:
            nome_file = '%s_%s_%s.xlsx' % (
                nome_file, slugify(aggiunta_titolo).lower(), slugify(timezone.now().date())
            )
        else:
            nome_file = 'proiezioni_statistiche_%s.xlsx' % slugify(timezone.now().date())
        response['Content-Disposition'] = 'attachment; filename=%s' % nome_file
        workbook.save(response)
        return response

    def stampa_catalogo_generale(self, request, queryset):
        elenco_religiosi = queryset
        template = get_template_stampa('catalogo_generale', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('catalogo_generale')
        elenco_province_stampa = []
        elenco_province_selezionate = elenco_religiosi.order_by('provincia_religiosa').values('provincia_religiosa').distinct()
        for provincia in elenco_province_selezionate:
            if provincia['provincia_religiosa']:
                provincia_religiosa = ProvinciaReligiosa.objects.get(id=provincia['provincia_religiosa'])
                elenco_case_selezionate = elenco_religiosi.filter(
                    provincia_religiosa=provincia_religiosa
                ).order_by(
                    'provincia_religiosa', 'casa_attuale'
                ).values('casa_attuale').distinct()
                elenco_case_stampa = []
                ordinamento_religiosi_stampa = preferences.ImpostazioniMagister.ordinamento_religiosi_stampa
                elenco_ordinamenti = None
                if ordinamento_religiosi_stampa:
                    elenco_ordinamenti = ordinamento_religiosi_stampa.split('__')
                for casa in elenco_case_selezionate:
                    if casa['casa_attuale']:
                        casa_attuale = CasaPublius.objects.get(id=casa['casa_attuale'])
                        if elenco_ordinamenti:
                            elenco_persone = elenco_religiosi.filter(
                                provincia_religiosa=provincia_religiosa,
                                casa_attuale=casa_attuale
                            ).order_by(*elenco_ordinamenti)
                        else:
                            elenco_persone = elenco_religiosi.filter(
                                provincia_religiosa=provincia_religiosa,
                                casa_attuale=casa_attuale
                            )
                        dict_casa = dict(casa=casa_attuale, elenco_religiosi=elenco_persone)
                        elenco_case_stampa.append(dict_casa)
                dict_provincia = dict(provincia=provincia_religiosa, elenco_case=elenco_case_stampa)
                elenco_province_stampa.append(dict_provincia)
        prima_colonna = preferences.ImpostazioniMagister.prima_colonna_stampa
        vedi_progressivo = False
        if prima_colonna == 'progressivo':
            vedi_progressivo = True
        context = Context(
            dict(
                elenco_province=elenco_province_stampa,
                vedi_progressivo=vedi_progressivo,
                nascondi_provincia=preferences.ImpostazioniMagister.nascondi_provincia_religiosa_default,
            )
        )
        filename = 'catalogo_generale_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    stampa_catalogo_generale.short_description = _('Stampa Catalogo Generale (sel.)')

    def stampa_elenco_semplice(self, request, queryset):
        elenco_religiosi = queryset
        template = get_template_stampa('elenco_semplice_religiosi', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('elenco_semplice_religiosi')
        elenco_province_stampa = []
        elenco_province_selezionate = elenco_religiosi.order_by('provincia_religiosa').values('provincia_religiosa').distinct()
        for provincia in elenco_province_selezionate:
            if provincia['provincia_religiosa']:
                provincia_religiosa = ProvinciaReligiosa.objects.get(id=provincia['provincia_religiosa'])
                ordinamento_religiosi_stampa = preferences.ImpostazioniMagister.ordinamento_religiosi_stampa
                elenco_ordinamenti = None
                if ordinamento_religiosi_stampa:
                    elenco_ordinamenti = ordinamento_religiosi_stampa.split('__')
                if elenco_ordinamenti:
                    elenco_persone = elenco_religiosi.filter(
                        provincia_religiosa=provincia_religiosa,
                    ).order_by(*elenco_ordinamenti)
                else:
                    elenco_persone = elenco_religiosi.filter(
                        provincia_religiosa=provincia_religiosa,
                    )
                dict_provincia = dict(provincia=provincia_religiosa, elenco_religiosi=elenco_persone)
                elenco_province_stampa.append(dict_provincia)
        prima_colonna = preferences.ImpostazioniMagister.prima_colonna_stampa
        vedi_progressivo = False
        if prima_colonna == 'progressivo':
            vedi_progressivo = True
        context = Context(
            dict(
                elenco_province=elenco_province_stampa,
                vedi_progressivo=vedi_progressivo,
                nascondi_provincia=preferences.ImpostazioniMagister.nascondi_provincia_religiosa_default,
            )
        )
        filename = 'elenco_semplice_religiosi_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    stampa_elenco_semplice.short_description = _('Stampa Elenco Semplice (sel.)')

    def stampa_dati_sanitari(self, request, queryset):
        elenco_religiosi = queryset
        template = get_template_stampa('dati_sanitari_religiosi', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('dati_sanitari_religiosi')
        elenco_province_stampa = []
        elenco_province_selezionate = elenco_religiosi.order_by('provincia_religiosa').values('provincia_religiosa').distinct()
        for provincia in elenco_province_selezionate:
            if provincia['provincia_religiosa']:
                provincia_religiosa = ProvinciaReligiosa.objects.get(id=provincia['provincia_religiosa'])
                ordinamento_religiosi_stampa = preferences.ImpostazioniMagister.ordinamento_religiosi_stampa
                elenco_ordinamenti = None
                if ordinamento_religiosi_stampa:
                    elenco_ordinamenti = ordinamento_religiosi_stampa.split('__')
                if elenco_ordinamenti:
                    elenco_persone = elenco_religiosi.filter(
                        provincia_religiosa=provincia_religiosa,
                    ).order_by(*elenco_ordinamenti)
                else:
                    elenco_persone = elenco_religiosi.filter(
                        provincia_religiosa=provincia_religiosa,
                    )
                dict_provincia = dict(provincia=provincia_religiosa, elenco_religiosi=elenco_persone)
                elenco_province_stampa.append(dict_provincia)
        prima_colonna = preferences.ImpostazioniMagister.prima_colonna_stampa
        vedi_progressivo = False
        if prima_colonna == 'progressivo':
            vedi_progressivo = True
        context = Context(
            dict(
                elenco_province=elenco_province_stampa,
                vedi_progressivo=vedi_progressivo,
                nascondi_provincia=preferences.ImpostazioniMagister.nascondi_provincia_religiosa_default,
            )
        )
        filename = 'dati_sanitari_religiosi_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    stampa_dati_sanitari.short_description = _('Stampa Elenco con Dati Sanitari (sel.)')

    def stampa_elenco_indirizzi(self, request, queryset):
        elenco_religiosi = queryset
        template = get_template_stampa('elenco_indirizzi_religiosi', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('elenco_indirizzi_religiosi')
        elenco_province_stampa = []
        elenco_province_selezionate = elenco_religiosi.order_by('provincia_religiosa').values('provincia_religiosa').distinct()
        for provincia in elenco_province_selezionate:
            if provincia['provincia_religiosa']:
                provincia_religiosa = ProvinciaReligiosa.objects.get(id=provincia['provincia_religiosa'])
                ordinamento_religiosi_stampa = preferences.ImpostazioniMagister.ordinamento_religiosi_stampa
                elenco_ordinamenti = None
                if ordinamento_religiosi_stampa:
                    elenco_ordinamenti = ordinamento_religiosi_stampa.split('__')
                if elenco_ordinamenti:
                    elenco_persone = elenco_religiosi.filter(
                        provincia_religiosa=provincia_religiosa,
                    ).order_by(*elenco_ordinamenti)
                else:
                    elenco_persone = elenco_religiosi.filter(
                        provincia_religiosa=provincia_religiosa,
                    )
                dict_provincia = dict(provincia=provincia_religiosa, elenco_religiosi=elenco_persone)
                elenco_province_stampa.append(dict_provincia)
        prima_colonna = preferences.ImpostazioniMagister.prima_colonna_stampa
        vedi_progressivo = False
        if prima_colonna == 'progressivo':
            vedi_progressivo = True
        context = Context(
            dict(
                elenco_province=elenco_province_stampa,
                vedi_progressivo=vedi_progressivo,
                nascondi_provincia=preferences.ImpostazioniMagister.nascondi_provincia_religiosa_default,
            )
        )
        filename = 'elenco_indirizzi_religiosi_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    stampa_elenco_indirizzi.short_description = _('Stampa Elenco Indirizzi (sel.)')

    def stampa_elenco_date(self, request, queryset):
        elenco_religiosi = queryset
        template = get_template_stampa('elenco_date_religiosi', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('elenco_date_religiosi')
        elenco_province_stampa = []
        elenco_province_selezionate = elenco_religiosi.order_by('provincia_religiosa').values('provincia_religiosa').distinct()
        for provincia in elenco_province_selezionate:
            if provincia['provincia_religiosa']:
                provincia_religiosa = ProvinciaReligiosa.objects.get(id=provincia['provincia_religiosa'])
                ordinamento_religiosi_stampa = preferences.ImpostazioniMagister.ordinamento_religiosi_stampa
                elenco_ordinamenti = None
                if ordinamento_religiosi_stampa:
                    elenco_ordinamenti = ordinamento_religiosi_stampa.split('__')
                if elenco_ordinamenti:
                    elenco_persone = elenco_religiosi.filter(
                        provincia_religiosa=provincia_religiosa,
                    ).order_by(*elenco_ordinamenti)
                else:
                    elenco_persone = elenco_religiosi.filter(
                        provincia_religiosa=provincia_religiosa,
                    )
                dict_provincia = dict(provincia=provincia_religiosa, elenco_religiosi=elenco_persone)
                elenco_province_stampa.append(dict_provincia)
        prima_colonna = preferences.ImpostazioniMagister.prima_colonna_stampa
        vedi_progressivo = False
        if prima_colonna == 'progressivo':
            vedi_progressivo = True
        context = Context(
            dict(
                elenco_province=elenco_province_stampa,
                vedi_progressivo=vedi_progressivo,
                nascondi_provincia=preferences.ImpostazioniMagister.nascondi_provincia_religiosa_default,
            )
        )
        filename = 'elenco_date_religiosi_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    stampa_elenco_date.short_description = _('Stampa Elenco Con Date (sel.)')

    def stampa_elenco_parenti(self, request, queryset):
        elenco_religiosi = queryset
        template = get_template_stampa('elenco_parenti_religiosi', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('elenco_parenti_religiosi')
        elenco_province_stampa = []
        elenco_province_selezionate = elenco_religiosi.order_by('provincia_religiosa').values('provincia_religiosa').distinct()
        for provincia in elenco_province_selezionate:
            if provincia['provincia_religiosa']:
                provincia_religiosa = ProvinciaReligiosa.objects.get(id=provincia['provincia_religiosa'])
                ordinamento_religiosi_stampa = preferences.ImpostazioniMagister.ordinamento_religiosi_stampa
                elenco_ordinamenti = None
                if ordinamento_religiosi_stampa:
                    elenco_ordinamenti = ordinamento_religiosi_stampa.split('__')
                if elenco_ordinamenti:
                    elenco_persone = elenco_religiosi.filter(
                        provincia_religiosa=provincia_religiosa,
                    ).order_by(*elenco_ordinamenti)
                else:
                    elenco_persone = elenco_religiosi.filter(
                        provincia_religiosa=provincia_religiosa,
                    )
                dict_provincia = dict(provincia=provincia_religiosa, elenco_religiosi=elenco_persone)
                elenco_province_stampa.append(dict_provincia)
        prima_colonna = preferences.ImpostazioniMagister.prima_colonna_stampa
        vedi_progressivo = False
        if prima_colonna == 'progressivo':
            vedi_progressivo = True
        context = Context(
            dict(
                elenco_province=elenco_province_stampa,
                vedi_progressivo=vedi_progressivo,
                nascondi_provincia=preferences.ImpostazioniMagister.nascondi_provincia_religiosa_default,
            )
        )
        filename = 'elenco_parenti_religiosi_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    stampa_elenco_parenti.short_description = _('Stampa Elenco Con Parenti (sel.)')

    def stampa_elenco_curriculum(self, request, queryset):
        elenco_religiosi = queryset
        template = get_template_stampa('elenco_curriculum_religiosi', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('elenco_curriculum_religiosi')
        elenco_province_stampa = []
        elenco_province_selezionate = elenco_religiosi.order_by('provincia_religiosa').values('provincia_religiosa').distinct()
        for provincia in elenco_province_selezionate:
            if provincia['provincia_religiosa']:
                provincia_religiosa = ProvinciaReligiosa.objects.get(id=provincia['provincia_religiosa'])
                ordinamento_religiosi_stampa = preferences.ImpostazioniMagister.ordinamento_religiosi_stampa
                elenco_ordinamenti = None
                if ordinamento_religiosi_stampa:
                    elenco_ordinamenti = ordinamento_religiosi_stampa.split('__')
                if elenco_ordinamenti:
                    elenco_persone = elenco_religiosi.filter(
                        provincia_religiosa=provincia_religiosa,
                    ).order_by(*elenco_ordinamenti)
                else:
                    elenco_persone = elenco_religiosi.filter(
                        provincia_religiosa=provincia_religiosa,
                    )
                dict_provincia = dict(provincia=provincia_religiosa, elenco_religiosi=elenco_persone)
                elenco_province_stampa.append(dict_provincia)
        prima_colonna = preferences.ImpostazioniMagister.prima_colonna_stampa
        vedi_progressivo = False
        if prima_colonna == 'progressivo':
            vedi_progressivo = True
        context = Context(
            dict(
                elenco_province=elenco_province_stampa,
                vedi_progressivo=vedi_progressivo,
                nascondi_provincia=preferences.ImpostazioniMagister.nascondi_provincia_religiosa_default,
            )
        )
        filename = 'elenco_curriculum_religiosi_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    stampa_elenco_curriculum.short_description = _('Stampa Elenco Con Curriculum (sel.)')

    def stampa_elenco_servizi(self, request, queryset):
        elenco_religiosi = queryset
        template = get_template_stampa('elenco_servizi_religiosi', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('elenco_servizi_religiosi')
        elenco_province_stampa = []
        elenco_province_selezionate = elenco_religiosi.order_by('provincia_religiosa').values('provincia_religiosa').distinct()
        for provincia in elenco_province_selezionate:
            if provincia['provincia_religiosa']:
                provincia_religiosa = ProvinciaReligiosa.objects.get(id=provincia['provincia_religiosa'])
                ordinamento_religiosi_stampa = preferences.ImpostazioniMagister.ordinamento_religiosi_stampa
                elenco_ordinamenti = None
                if ordinamento_religiosi_stampa:
                    elenco_ordinamenti = ordinamento_religiosi_stampa.split('__')
                if elenco_ordinamenti:
                    elenco_persone = elenco_religiosi.filter(
                        provincia_religiosa=provincia_religiosa,
                    ).order_by(*elenco_ordinamenti)
                else:
                    elenco_persone = elenco_religiosi.filter(
                        provincia_religiosa=provincia_religiosa,
                    )
                dict_provincia = dict(provincia=provincia_religiosa, elenco_religiosi=elenco_persone)
                elenco_province_stampa.append(dict_provincia)
        prima_colonna = preferences.ImpostazioniMagister.prima_colonna_stampa
        vedi_progressivo = False
        if prima_colonna == 'progressivo':
            vedi_progressivo = True
        context = Context(
            dict(
                elenco_province=elenco_province_stampa,
                vedi_progressivo=vedi_progressivo,
                nascondi_provincia=preferences.ImpostazioniMagister.nascondi_provincia_religiosa_default,
            )
        )
        filename = 'elenco_servizi_religiosi_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    stampa_elenco_servizi.short_description = _('Stampa Elenco Con Servizi Svolti (sel.)')

    def stampa_elenco_studi(self, request, queryset):
        elenco_religiosi = queryset
        template = get_template_stampa('elenco_studi_religiosi', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('elenco_studi_religiosi')
        elenco_province_stampa = []
        elenco_province_selezionate = elenco_religiosi.order_by('provincia_religiosa').values('provincia_religiosa').distinct()
        for provincia in elenco_province_selezionate:
            if provincia['provincia_religiosa']:
                provincia_religiosa = ProvinciaReligiosa.objects.get(id=provincia['provincia_religiosa'])
                ordinamento_religiosi_stampa = preferences.ImpostazioniMagister.ordinamento_religiosi_stampa
                elenco_ordinamenti = None
                if ordinamento_religiosi_stampa:
                    elenco_ordinamenti = ordinamento_religiosi_stampa.split('__')
                if elenco_ordinamenti:
                    elenco_persone = elenco_religiosi.filter(
                        provincia_religiosa=provincia_religiosa,
                    ).order_by(*elenco_ordinamenti)
                else:
                    elenco_persone = elenco_religiosi.filter(
                        provincia_religiosa=provincia_religiosa,
                    )
                dict_provincia = dict(provincia=provincia_religiosa, elenco_religiosi=elenco_persone)
                elenco_province_stampa.append(dict_provincia)
        prima_colonna = preferences.ImpostazioniMagister.prima_colonna_stampa
        vedi_progressivo = False
        if prima_colonna == 'progressivo':
            vedi_progressivo = True
        context = Context(
            dict(
                elenco_province=elenco_province_stampa,
                vedi_progressivo=vedi_progressivo,
                nascondi_provincia=preferences.ImpostazioniMagister.nascondi_provincia_religiosa_default,
            )
        )
        filename = 'elenco_studi_religiosi_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    stampa_elenco_studi.short_description = _('Stampa Elenco Con Titoli di studio (sel.)')

    def stampa_elenco_compleanni(self, request, queryset):
        elenco_religiosi = queryset
        template = get_template_stampa('elenco_compleanni_religiosi', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('elenco_compleanni_religiosi')
        elenco_province_stampa = []
        elenco_province_selezionate = elenco_religiosi.order_by('provincia_religiosa').values('provincia_religiosa').distinct()
        for provincia in elenco_province_selezionate:
            if provincia['provincia_religiosa']:
                provincia_religiosa = ProvinciaReligiosa.objects.get(id=provincia['provincia_religiosa'])
                elenco_persone_provincia = elenco_religiosi.filter(
                    provincia_religiosa=provincia_religiosa,
                ).order_by('data_nascita__month', 'data_nascita__day')
                dict_provincia = dict(provincia=provincia_religiosa, elenco_religiosi_provincia=[])
                for persona in elenco_persone_provincia:
                    if persona.data_nascita:
                        mese_stringa = _(persona.data_nascita.strftime("%B"))
                        giorno = persona.data_nascita.day
                        mese_trovato = False
                        for elenco_religiosi_mese in dict_provincia['elenco_religiosi_provincia']:
                            if elenco_religiosi_mese['mese'] == mese_stringa:
                                mese_trovato = True
                                giorno_trovato = False
                                for elenco_religiosi_giorno in elenco_religiosi_mese['elenco_giorni']:
                                    if elenco_religiosi_giorno['giorno'] == giorno:
                                        giorno_trovato = True
                                        elenco_religiosi_giorno['religiosi'].append(persona)
                                if not giorno_trovato:
                                    elenco_religiosi_mese['elenco_giorni'].append({'giorno': giorno, 'religiosi': [persona, ]})
                        if not mese_trovato:
                            religioso_giorno = {'giorno': giorno, 'religiosi': [persona, ]}
                            dict_provincia['elenco_religiosi_provincia'].append({'mese': mese_stringa, 'elenco_giorni': [religioso_giorno, ]})
                elenco_province_stampa.append(dict_provincia)
        prima_colonna = preferences.ImpostazioniMagister.prima_colonna_stampa
        vedi_progressivo = False
        if prima_colonna == 'progressivo':
            vedi_progressivo = True
        context = Context(
            dict(
                elenco_province=elenco_province_stampa,
                vedi_progressivo=vedi_progressivo,
                nascondi_provincia=preferences.ImpostazioniMagister.nascondi_provincia_religiosa_default,
            )
        )
        filename = 'elenco_compleanni_religiosi%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    stampa_elenco_compleanni.short_description = _('Stampa Elenco Compleanni (sel.)')

    def stampa_rubrica_telefonica(self, request, queryset):
        elenco_religiosi = queryset
        template = get_template_stampa('rubrica_telefonica_religiosi', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('rubrica_telefonica_religiosi')
        elenco_province_stampa = []
        elenco_province_selezionate = elenco_religiosi.order_by('provincia_religiosa').values('provincia_religiosa').distinct()
        for provincia in elenco_province_selezionate:
            if provincia['provincia_religiosa']:
                provincia_religiosa = ProvinciaReligiosa.objects.get(id=provincia['provincia_religiosa'])
                ordinamento_religiosi_stampa = preferences.ImpostazioniMagister.ordinamento_religiosi_stampa
                elenco_ordinamenti = None
                if ordinamento_religiosi_stampa:
                    elenco_ordinamenti = ordinamento_religiosi_stampa.split('__')
                if elenco_ordinamenti:
                    elenco_persone = elenco_religiosi.filter(
                        provincia_religiosa=provincia_religiosa,
                    ).order_by(*elenco_ordinamenti)
                else:
                    elenco_persone = elenco_religiosi.filter(
                        provincia_religiosa=provincia_religiosa,
                    )
                dict_provincia = dict(provincia=provincia_religiosa, elenco_religiosi=elenco_persone)
                elenco_province_stampa.append(dict_provincia)
        prima_colonna = preferences.ImpostazioniMagister.prima_colonna_stampa
        vedi_progressivo = False
        if prima_colonna == 'progressivo':
            vedi_progressivo = True
        context = Context(
            dict(
                elenco_province=elenco_province_stampa,
                vedi_progressivo=vedi_progressivo,
                nascondi_provincia=preferences.ImpostazioniMagister.nascondi_provincia_religiosa_default,
            )
        )
        filename = 'rubrica_telefonica_religiosi_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    stampa_rubrica_telefonica.short_description = _('Stampa Rubrica Telefonica (sel.)')

    def stampa_elenco_defunti(self, request, queryset):
        elenco_defunti = queryset.filter(data_morte__isnull=False)
        template = get_template_stampa('elenco_defunti_religiosi', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('elenco_defunti_religiosi')
        elenco_province_stampa = []
        elenco_province_selezionate = elenco_defunti.order_by('provincia_religiosa').values('provincia_religiosa').distinct()
        for provincia in elenco_province_selezionate:
            if provincia['provincia_religiosa']:
                provincia_religiosa = ProvinciaReligiosa.objects.get(id=provincia['provincia_religiosa'])
                ordinamento_religiosi_stampa = preferences.ImpostazioniMagister.ordinamento_religiosi_stampa
                elenco_ordinamenti = None
                if ordinamento_religiosi_stampa:
                    elenco_ordinamenti = ordinamento_religiosi_stampa.split('__')
                if elenco_ordinamenti:
                    elenco_persone = elenco_defunti.filter(
                        provincia_religiosa=provincia_religiosa,
                    ).order_by(*elenco_ordinamenti)
                else:
                    elenco_persone = elenco_defunti.filter(
                        provincia_religiosa=provincia_religiosa,
                    )
                dict_provincia = dict(provincia=provincia_religiosa, elenco_religiosi=elenco_persone)
                elenco_province_stampa.append(dict_provincia)
        prima_colonna = preferences.ImpostazioniMagister.prima_colonna_stampa
        vedi_progressivo = False
        if prima_colonna == 'progressivo':
            vedi_progressivo = True
        context = Context(
            dict(
                elenco_province=elenco_province_stampa,
                vedi_progressivo=vedi_progressivo,
                nascondi_provincia=preferences.ImpostazioniMagister.nascondi_provincia_religiosa_default,
            )
        )
        filename = 'elenco_defunti_religiosi_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    stampa_elenco_defunti.short_description = _('Stampa Elenco Defunti (sel.)')

    def stampa_ricorrenze_defunti(self, request, queryset):
        elenco_defunti = queryset.filter(data_morte__isnull=False)
        template = get_template_stampa('ricorrenze_defunti_religiosi', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('ricorrenze_defunti_religiosi')
        elenco_province_stampa = []
        elenco_province_selezionate = elenco_defunti.order_by('provincia_religiosa').values('provincia_religiosa').distinct()
        for provincia in elenco_province_selezionate:
            if provincia['provincia_religiosa']:
                provincia_religiosa = ProvinciaReligiosa.objects.get(id=provincia['provincia_religiosa'])
                elenco_persone_provincia = elenco_defunti.filter(
                    provincia_religiosa=provincia_religiosa,
                ).order_by('data_morte__month', 'data_morte__day')
                dict_provincia = dict(provincia=provincia_religiosa, elenco_religiosi_provincia=[])
                for persona in elenco_persone_provincia:
                    if persona.data_morte:
                        mese_stringa = _(persona.data_morte.strftime("%B"))
                        giorno = persona.data_morte.day
                        mese_trovato = False
                        for elenco_religiosi_mese in dict_provincia['elenco_religiosi_provincia']:
                            if elenco_religiosi_mese['mese'] == mese_stringa:
                                mese_trovato = True
                                giorno_trovato = False
                                for elenco_religiosi_giorno in elenco_religiosi_mese['elenco_giorni']:
                                    if elenco_religiosi_giorno['giorno'] == giorno:
                                        giorno_trovato = True
                                        elenco_religiosi_giorno['religiosi'].append(persona)
                                if not giorno_trovato:
                                    elenco_religiosi_mese['elenco_giorni'].append({'giorno': giorno, 'religiosi': [persona, ]})
                        if not mese_trovato:
                            religioso_giorno = {'giorno': giorno, 'religiosi': [persona, ]}
                            dict_provincia['elenco_religiosi_provincia'].append({'mese': mese_stringa, 'elenco_giorni': [religioso_giorno, ]})
                elenco_province_stampa.append(dict_provincia)
        prima_colonna = preferences.ImpostazioniMagister.prima_colonna_stampa
        vedi_progressivo = False
        if prima_colonna == 'progressivo':
            vedi_progressivo = True
        context = Context(
            dict(
                elenco_province=elenco_province_stampa,
                vedi_progressivo=vedi_progressivo,
                nascondi_provincia=preferences.ImpostazioniMagister.nascondi_provincia_religiosa_default,
            )
        )
        filename = 'ricorrenze_defunti_religiosi_%s_%s.odt' % (slugify(request.user.area_corrente).upper(), slugify(timezone.now().date()))
        return webodt_render_to_response(
            template, context_instance=context,
            filename=filename
        )
    stampa_ricorrenze_defunti.short_description = _('Stampa Ricorrenze Defunti (sel.)')

    def vedi_grafico_status_religioso(self, request, queryset):
        return StatusReligiosoPersoneView.as_view()(request, queryset=queryset)
    vedi_grafico_status_religioso.short_description = _('Vedi Grafico Status Religioso')

    def vedi_grafico_status(self, request, queryset):
        return StatusPersoneView.as_view()(request, queryset=queryset)
    vedi_grafico_status.short_description = _('Vedi Grafico per Status')

    def vedi_grafico_provincia_religiosa(self, request, queryset):
        return ProvinciaReligiosaView.as_view()(request, queryset=queryset)
    vedi_grafico_provincia_religiosa.short_description = _('Vedi Grafico per Provincia Religiosa')

    def vedi_grafico_eta_religiosi(self, request, queryset):
        return FasceEtaReligiosiView.as_view()(request, queryset=queryset)
    vedi_grafico_eta_religiosi.short_description = _('Vedi Grafico Età Religiosi')

    def vedi_grafico_case_religiosi(self, request, queryset):
        return ReligiosiPerCasaView.as_view()(request, queryset=queryset)
    vedi_grafico_case_religiosi.short_description = _('Vedi Grafico Case Religiosi')

    def stampa_schede_complete(self, request, queryset):
        template = get_template_stampa('scheda_completa_religiosi', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('scheda_completa_religiosi')
        elenco_religiosi = []
        if queryset:
            for indice in range(0, queryset.count()):
                religioso = queryset[indice]
                if indice == (queryset.count() - 1):
                    religioso.is_ultimo = True
                elenco_religiosi.append(religioso)
        context = {
            'elenco_religiosi': elenco_religiosi,
        }
        if template[-3:] == 'odt':
            engine = Renderer()
            result = engine.render(template, **context)
            response = HttpResponse(content_type='application/vnd.oasis.opendocument.text')
            filename = 'scheda_completa_religiosi_%s_%s.odt' % (
                slugify(request.user.area_corrente).upper(), slugify(timezone.now().date())
            )
            response['Content-Disposition'] = 'attachment; filename=%s' % filename
            response.write(result)
        elif template[-4:] == 'docx':
            doc = DocxTemplate(template)
            elenco_religiosi_foto = []
            for rel in elenco_religiosi:
                foto_religioso = ''
                if rel.fotografia:
                    nuovo_file = tempfile.mktemp(suffix='.jpg')
                    from PIL import Image
                    im = Image.open(rel.fotografia.file)
                    rgb_im = im.convert('RGB')
                    rgb_im.save(nuovo_file)
                    foto_religioso = InlineImage(doc, nuovo_file , width=Mm(35), height=Mm(45))
                elenco_religiosi_foto.append({'rel': rel, 'foto': foto_religioso})
            context = {
                'elenco_religiosi_foto': elenco_religiosi_foto,
            }
            filename = 'scheda_completa_religiosi_%s_%s.docx' % (
                slugify(request.user.area_corrente).upper(), slugify(timezone.now().date())
            )
            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            )
            doc.render(context)
            doc.save(response)
            response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response
    stampa_schede_complete.short_description = _('Stampa Scheda Completa (sel.)')

    def stampa_annuario_familiari(self, request):
        intestazione_stampe = preferences.ImpostazioniMagister.intestazione_stampe
        ordine_campi_stampa = preferences.ImpostazioniMagister.ordine_campi_stampa
        template = get_template_stampa('religiosi_annuario_familiari', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('religiosi_annuario_familiari')
        voci_curriculum_postulanti_novizie = VoceCurriculum.objects.filter(
            Q(nome__icontains='postulant') | Q(nome__icontains='novizi')
        )
        elenco_viventi = Religioso.objects.filter(
            data_morte__isnull=True, data_uscita__isnull=True
        ).select_related(
            'nazione_nascita', 'provincia_nascita', 'provincia_religiosa',
        ).exclude(
            status_religioso__in=voci_curriculum_postulanti_novizie
        ).order_by('appellativo', 'cognome', 'nome')
        elenco_parenti = Familiare.objects.filter(
            religioso__in=elenco_viventi
        ).select_related(
            'religioso',
        ).order_by('religioso__appellativo', 'religioso__cognome', 'religioso__nome')
        dizionario_familiari = {}
        for parente in elenco_parenti:
            if parente.religioso_id not in dizionario_familiari:
                dizionario_familiari[parente.religioso_id] = []
            dizionario_familiari[parente.religioso_id].append(parente)
        for vivente in elenco_viventi:
            vivente.nome_stampa_con_codice = vivente.get_nome_stampa_con_codice(ordine_campi_stampa)
            vivente.dati_indirizzi_stampa = vivente.get_dati_indirizzi_stampa()
            if vivente.id in dizionario_familiari:
                vivente.elenco_parenti = dizionario_familiari[vivente.id]
                vivente.dati_parenti_stampa = vivente.get_dati_parenti_stampa()
        contesto = dict(
            numero_religiosi=elenco_viventi.count(),
            elenco_viventi=elenco_viventi,
            intestazione_stampe=intestazione_stampe,
            data_corrente=timezone.now().date().strftime('%d/%m/%Y'),
        )
        filename = 'annuario_familiari_%s_%s.docx' % (
            slugify(request.user.area_corrente).upper(), slugify(timezone.now().date())
        )
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        doc = DocxTemplate(template)
        doc.render(contesto)
        doc.save(response)
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response
    stampa_annuario_familiari.short_description = _('Stampa Annuario Familiari')

    def stampa_quadro_riassuntivo(self, request):
        intestazione_stampe = preferences.ImpostazioniMagister.intestazione_stampe
        ordine_campi_stampa = preferences.ImpostazioniMagister.ordine_campi_stampa
        template = get_template_stampa('religiosi_quadro_riassuntivo', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('religiosi_quadro_riassuntivo')
        # Elenco dei religiosi viventi
        elenco_viventi = Religioso.objects.filter(
            data_morte__isnull=True, data_uscita__isnull=True
        )
        voci_suore = VoceCurriculum.objects.exclude(tipologia_percorso__in=['postulante', 'novizia', 'aspirante'])
        elenco_suore_comboniane = elenco_viventi.filter(status_religioso__in=voci_suore)
        # Elenco dei voti temporanei
        voce_voti_temporanei = VoceCurriculum.objects.filter(tipologia_percorso='voti_temporanei')
        elenco_voti_temporanei = elenco_viventi.filter(status_religioso__in=voce_voti_temporanei)
        # Elenco dei voti perpetui
        voce_voti_perpetui = VoceCurriculum.objects.filter(tipologia_percorso='voti_perpetui')
        elenco_voti_perpetui = elenco_viventi.filter(status_religioso__in=voce_voti_perpetui)
        # Elenco delle case attive
        elenco_case_attive = CasaPublius.objects.filter(attiva=True)
        elenco_tipi_case = TipoCasa.objects.all()
        # Numero di case per tipo
        numero_case_per_tipo = []
        for tipo_casa in elenco_tipi_case:
            numero_case = elenco_case_attive.filter(tipo_casa=tipo_casa).count()
            numero_case_per_tipo.append((tipo_casa, numero_case))
        # Elenco dei voti temporanei per area/circoscrizione
        elenco_aree_circoscrizioni = []
        tipo_area_circoscrizione = TipoArea.objects.get(nome__icontains='circoscrizione')
        if tipo_area_circoscrizione:
            for area in Area.objects.filter(tipo_area=tipo_area_circoscrizione).order_by('nome'):
                aree_figlie = area.get_descendants(include_self=True)
                elenco_case_area = elenco_case_attive.filter(area__in=aree_figlie)
                elenco_vt_case = elenco_voti_temporanei.filter(casa_attuale__in=elenco_case_area)
                numero_voti_temporanei = elenco_vt_case.count()
                if numero_voti_temporanei:
                    dati_area = dict()
                    dati_area['area'] = area
                    dati_area['numero_religiosi'] = numero_voti_temporanei
                    elenco_aree_circoscrizioni.append(dati_area)
        # Elenco dei postulanti
        voci_postulanti = VoceCurriculum.objects.filter(tipologia_percorso='postulante')
        elenco_postulanti = elenco_viventi.filter(status_religioso__in=voci_postulanti)
        totale_postulanti = elenco_postulanti.count()
        elenco_dati_postulanti = []
        for voce in voci_postulanti:
            dati_postulante = dict()
            dati_postulante['nome'] = voce
            dati_postulante['elenco_nazioni'] = []
            elenco_postulanti_voce = elenco_postulanti.filter(status_religioso=voce)
            nazioni_postulanti_voce = elenco_postulanti_voce.values_list('casa_attuale__nazione', flat=True).distinct().order_by('casa_attuale__nazione__continente', 'casa_attuale__nazione__nome')
            for nazione_id in nazioni_postulanti_voce:
                nazione = Stato.objects.get(id=nazione_id)
                dati_nazione = dict()
                dati_nazione['continente'] = nazione.get_continente_display()
                dati_nazione['nazione'] = nazione
                dati_nazione['totale'] = elenco_postulanti_voce.filter(casa_attuale__nazione=nazione).count()
                dati_postulante['elenco_nazioni'].append(dati_nazione)
            if dati_postulante['elenco_nazioni']:
                elenco_dati_postulanti.append(dati_postulante)
        # Elenco delle novizie
        voci_novizie = VoceCurriculum.objects.filter(tipologia_percorso='novizia')
        elenco_novizie = elenco_viventi.filter(status_religioso__in=voci_novizie)
        totale_novizie = elenco_novizie.count()
        elenco_dati_novizie = []
        for voce in voci_novizie:
            dati_novizia = dict()
            dati_novizia['nome'] = voce
            dati_novizia['elenco_nazioni'] = []
            elenco_novizie_voce = elenco_novizie.filter(status_religioso=voce)
            nazioni_novizie_voce = elenco_novizie_voce.values_list('casa_attuale__nazione', flat=True).distinct().order_by('casa_attuale__nazione__continente', 'casa_attuale__nazione__nome')
            for nazione_id in nazioni_novizie_voce:
                nazione = Stato.objects.get(id=nazione_id)
                dati_nazione = dict()
                dati_nazione['continente'] = nazione.get_continente_display()
                dati_nazione['nazione'] = nazione
                dati_nazione['totale'] = elenco_novizie_voce.filter(casa_attuale__nazione=nazione).count()
                dati_novizia['elenco_nazioni'].append(dati_nazione)
            if dati_novizia['elenco_nazioni']:
                elenco_dati_novizie.append(dati_novizia)
        # Distribuzione delle suore per provincia
        elenco_distribuzioni_province = []
        for area in Area.objects.filter(tipo_area=tipo_area_circoscrizione).order_by('nome'):
            aree_figlie = area.get_descendants(include_self=True)
            suore_area = elenco_suore_comboniane.filter(casa_attuale__area__in=aree_figlie)
            if suore_area:
                distribuzione_provincia = dict()
                distribuzione_provincia['codice'] = area.codice
                distribuzione_provincia['nome'] = area
                distribuzione_provincia['totale'] = suore_area.count()
                distribuzione_provincia['elenco_paesi'] = []
                elenco_aree_suore = suore_area.values_list('casa_attuale__area', flat=True).distinct().order_by('casa_attuale__area')
                for area_id in elenco_aree_suore:
                    area = Area.objects.get(id=area_id)
                    dati_area = dict()
                    dati_area['nome'] = area.nome
                    dati_area['totale_suore'] = suore_area.filter(casa_attuale__area=area).count()
                    dati_area['totale_comunita'] = CasaPublius.objects.filter(attiva=True, area=area).count()
                    distribuzione_provincia['elenco_paesi'].append(dati_area)
                elenco_distribuzioni_province.append(distribuzione_provincia)
        # Elenco dellle suore per continente e nazione
         # Recupera solo le case attive con nazioni e continenti
        case_attive = CasaPublius.objects.filter(attiva=True)
        # Raggruppamento case per nazione
        case_per_nazione = (
            case_attive.values('nazione')
            .annotate(totale_comunita=Count('id', distinct=True))
            .order_by('nazione__continente', 'nazione__nome')
        )
        # Raggruppamento suore per nazione (con casa attuale valida)
        suore_per_nazione = (
            elenco_suore_comboniane.filter(casa_attuale__isnull=False)
            .values('casa_attuale__nazione')
            .annotate(totale_suore=Count('id', distinct=True))
            .order_by('casa_attuale__nazione__continente', 'casa_attuale__nazione__nome')
        )
        # Ottieni l’elenco completo degli ID di nazioni coinvolte
        nazioni_ids = set(entry['nazione'] for entry in case_per_nazione) | \
                    set(entry['casa_attuale__nazione'] for entry in suore_per_nazione)
        # Recupera tutte le nazioni in un solo colpo
        stati_map = Stato.objects.in_bulk(nazioni_ids)
        # Mappatura codice → nome del continente
        CONTINENTE_LABELS = dict(Stato._meta.get_field('continente').choices)
        # Costruzione mappa nazione_id → dati aggregati
        nazioni_data = {}
        for entry in case_per_nazione:
            nazione_id = entry['nazione']
            stato = stati_map.get(nazione_id)
            if stato:
                nazioni_data[nazione_id] = {
                    'nome': stato.descrizione,
                    'totale_comunita': entry['totale_comunita'],
                    'totale_suore': 0,
                    'continente_id': stato.continente
                }
        for entry in suore_per_nazione:
            nazione_id = entry['casa_attuale__nazione']
            stato = stati_map.get(nazione_id)
            if stato:
                if nazione_id not in nazioni_data:
                    nazioni_data[nazione_id] = {
                        'nome': stato.descrizione,
                        'totale_comunita': 0,
                        'totale_suore': 0,
                        'continente_id': stato.continente
                    }
                nazioni_data[nazione_id]['totale_suore'] = entry['totale_suore']
        # Aggregazione per continente
        continenti_data = {}
        for nazione in nazioni_data.values():
            continente_id = nazione['continente_id']
            if continente_id not in continenti_data:
                continenti_data[continente_id] = {
                    'nome': CONTINENTE_LABELS.get(continente_id, str(continente_id)),
                    'totale_comunita': 0,
                    'totale_suore': 0,
                    'elenco_nazioni': []
                }
            continenti_data[continente_id]['totale_comunita'] += nazione['totale_comunita']
            continenti_data[continente_id]['totale_suore'] += nazione['totale_suore']
            continenti_data[continente_id]['elenco_nazioni'].append({
                'nome': nazione['nome'],
                'totale_comunita': nazione['totale_comunita'],
                'totale_suore': nazione['totale_suore']
            })

        # Conversione finale a lista ordinata per nome continente
        elenco_distribuzioni_continenti = sorted(
            continenti_data.values(),
            key=lambda c: c['nome']
        )
        # Contesto per il template
        contesto = dict(
            totale_religiosi=elenco_suore_comboniane.count(),
            totale_voti_temporanei=elenco_voti_temporanei.count(),
            totale_voti_perpetui=elenco_voti_perpetui.count(),
            numero_case_per_tipo=numero_case_per_tipo,
            totale_vt_circoscrizioni=elenco_voti_temporanei.count(),
            elenco_aree_circoscrizioni=elenco_aree_circoscrizioni,
            elenco_dati_postulanti=elenco_dati_postulanti,
            totale_postulanti=totale_postulanti,
            totale_novizie=totale_novizie,
            elenco_distribuzioni_province=elenco_distribuzioni_province,
            elenco_distribuzioni_continenti=elenco_distribuzioni_continenti,
            elenco_dati_novizie=elenco_dati_novizie,
            intestazione_stampe=intestazione_stampe,
            data_corrente=timezone.now().date().strftime('%d/%m/%Y'),
        )
        filename = 'quadro_riassuntivo_%s_%s.docx' % (
            slugify(request.user.area_corrente).upper(), slugify(timezone.now().date())
        )
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        doc = DocxTemplate(template)
        doc.render(contesto)
        doc.save(response)
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response
    stampa_quadro_riassuntivo.short_description = _('Stampa Quadro Riassuntivo')

    def stampa_annuario_defunti(self, request):
        intestazione_stampe = preferences.ImpostazioniMagister.intestazione_stampe
        template = get_template_stampa('religiosi_annuario_defunti', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('religiosi_annuario_defunti')
        voci_curriculum_postulanti_novizie = VoceCurriculum.objects.filter(
            Q(nome__icontains='postulant') | Q(nome__icontains='novizi')
        )
        elenco_defunti = Religioso.objects.filter(data_morte__isnull=False).select_related('nazione_morte').exclude(
            status_religioso__in=voci_curriculum_postulanti_novizie
        ).order_by('appellativo', 'cognome', 'nome')
        ordine_campi_stampa = preferences.ImpostazioniMagister.ordine_campi_stampa
        for defunto in elenco_defunti:
            defunto.nome_stampa = defunto.get_nome_stampa(ordine_campi_stampa)
            defunto.dati_morte_stampa = defunto.get_dati_morte_stampa()
        contesto = dict(
            elenco_defunti=elenco_defunti,
            numero_religiosi=elenco_defunti.count(),
            intestazione_stampe=intestazione_stampe,
            data_corrente=timezone.now().date().strftime('%d/%m/%Y'),
        )
        filename = 'annuario_defunti_%s_%s.docx' % (
            slugify(request.user.area_corrente).upper(), slugify(timezone.now().date())
        )
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        doc = DocxTemplate(template)
        doc.render(contesto)
        doc.save(response)
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response
    stampa_annuario_defunti.short_description = _('Stampa Annuario Defunti')

    def stampa_annuario_componenti_case_vecchio(self, request):
        intestazione_stampe = preferences.ImpostazioniMagister.intestazione_stampe
        template = get_template_stampa('religiosi_annuario_componenti_case', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('religiosi_annuario_componenti_case')
        elenco_province_religiose = ProvinciaReligiosa.objects.all()
        elenco_province_case = []
        for provincia_religiosa in elenco_province_religiose:
            elenco_case = provincia_religiosa.get_elenco_case()
            if elenco_case:
                provincia_religiosa.elenco_case = elenco_case
                for casa in elenco_case:
                    casa.elenco_componenti_annuario = casa.get_elenco_componenti_annuario(provincia_religiosa.id)
                provincia_religiosa.numero_case = elenco_case.count()
                elenco_religiosi_provincia = Religioso.objects.filter(
                    provincia_religiosa=provincia_religiosa, data_morte__isnull=True, data_uscita__isnull=True
                ).order_by('appellativo', 'cognome', 'nome')
                elenco_nomi_religiosi = ''
                for religioso in elenco_religiosi_provincia:
                    elenco_nomi_religiosi += '%s\n' % religioso.get_nome_stampa()
                provincia_religiosa.elenco_nomi_religiosi = elenco_nomi_religiosi
                provincia_religiosa.numero_religiosi = elenco_religiosi_provincia.count()
                elenco_province_case.append(provincia_religiosa)
        contesto = dict(
            elenco_province_case=elenco_province_case,
            intestazione_stampe=intestazione_stampe,
            data_corrente=timezone.now().date().strftime('%d/%m/%Y'),
        )
        filename = 'annuario_componenti_case_%s_%s.docx' % (
            slugify(request.user.area_corrente).upper(), slugify(timezone.now().date())
        )
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        doc = DocxTemplate(template)
        doc.render(contesto)
        doc.save(response)
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response
    stampa_annuario_componenti_case_vecchio.short_description = _('Stampa Annuario Componenti Case (vecchio)')

    def stampa_annuario_componenti_case(self, request):
        intestazione_stampe = preferences.ImpostazioniMagister.intestazione_stampe
        template = get_template_stampa('religiosi_annuario_componenti_case', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('religiosi_annuario_componenti_case')
        elenco_province_case = []
        elenco_case_area_nazione = CasaPublius.objects.all().values('area', 'nazione').order_by('area__nome', 'nazione').distinct()
        voci_curriculum_postulanti_novizie = VoceCurriculum.objects.filter(
            Q(nome__icontains='postulant') | Q(nome__icontains='novizi')
        )
        for area_nazione in elenco_case_area_nazione:
            area_nazione['area_obj'] = Area.objects.get(id=area_nazione['area'])
            area_nazione['nazione_obj'] = Stato.objects.get(id=area_nazione['nazione'])
            elenco_case = CasaPublius.objects.filter(area=area_nazione['area'], nazione=area_nazione['nazione']).order_by('nome')
            if elenco_case:
                elenco_case_popolate = []
                for casa in elenco_case:
                    elenco_componenti_annuario = casa.get_elenco_componenti_annuario(voci_curriculum_esclusi=voci_curriculum_postulanti_novizie)
                    if elenco_componenti_annuario:
                        casa.elenco_componenti_annuario = casa.get_elenco_componenti_annuario(voci_curriculum_esclusi=voci_curriculum_postulanti_novizie)
                        elenco_case_popolate.append(casa)
                if elenco_case_popolate:
                    elenco_religiosi_provincia = Religioso.objects.filter(
                        casa_attuale__area=area_nazione['area'], 
                        casa_attuale__nazione=area_nazione['nazione'], 
                        data_morte__isnull=True, data_uscita__isnull=True,
                    ).exclude(
                        status_religioso__in=voci_curriculum_postulanti_novizie
                    ).order_by('appellativo', 'cognome', 'nome')
                    numero_religiosi = elenco_religiosi_provincia.count()
                    if numero_religiosi:
                        elenco_nomi_religiosi = ''
                        for religioso in elenco_religiosi_provincia:
                            elenco_nomi_religiosi += '%s\n' % religioso.get_nome_stampa()
                        area_nazione['elenco_nomi_religiosi'] = elenco_nomi_religiosi
                        area_nazione['elenco_case'] = elenco_case_popolate
                        area_nazione['numero_case'] = len(elenco_case_popolate)
                        area_nazione['numero_religiosi'] = numero_religiosi
                        elenco_province_case.append(area_nazione)
        contesto = dict(
            elenco_province_case=elenco_province_case,
            intestazione_stampe=intestazione_stampe,
            data_corrente=timezone.now().date().strftime('%d/%m/%Y'),
        )
        filename = 'annuario_componenti_case_%s_%s.docx' % (
            slugify(request.user.area_corrente).upper(), slugify(timezone.now().date())
        )
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        doc = DocxTemplate(template)
        doc.render(contesto)
        doc.save(response)
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response
    stampa_annuario_componenti_case.short_description = _('Stampa Annuario Componenti Case')

    def stampa_annuario_componenti_case_per_area(self, request):
        intestazione_stampe = preferences.ImpostazioniMagister.intestazione_stampe
        template = get_template_stampa('religiosi_annuario_per_aree', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('religiosi_annuario_per_aree')

        # Parti dall'area corrente dell'utente
        area_corrente = request.user.area_corrente

        # Ottimizzazione: Pre-carica tutti i dati necessari con query ottimizzate
        # Ottieni tutte le aree discendenti (inclusa quella corrente)
        aree_da_processare = area_corrente.get_descendants(include_self=True).select_related('tipo_area')

        # Pre-carica tutte le case con i loro dati in una sola query
        # Filtra solo le case attive
        case_per_area = {}
        case_queryset = CasaPublius.objects.filter(
            area__in=aree_da_processare,
            attiva=True
        ).select_related('area').order_by('nome')

        for casa in case_queryset:
            if casa.area_id not in case_per_area:
                case_per_area[casa.area_id] = []
            case_per_area[casa.area_id].append(casa)



        elenco_aree_gerarchiche = []

        def processa_area_singola(area, livello=0):
            """Processa una singola area e restituisce i suoi dati"""
            # Usa i dati pre-caricati
            elenco_case = case_per_area.get(area.id, [])

            # Processa solo le case che hanno almeno un componente dell'annuario
            elenco_case_popolate = []
            for casa in elenco_case:
                # Usa la nuova funzione che identifica i superiori tramite status con formattazione grassetto
                elenco_componenti_annuario = casa.get_elenco_componenti_annuario_per_status(con_formattazione=True)
                if elenco_componenti_annuario:
                    casa.elenco_componenti_annuario = elenco_componenti_annuario
                    elenco_case_popolate.append(casa)

            # IMPORTANTE: Crea sempre i dati dell'area, anche se non ha case o religiosi
            # Questo assicura che tutti i livelli siano sempre mostrati
            area_data = {
                'area_obj': area,
                'elenco_case': elenco_case_popolate,
                'numero_case': len(elenco_case_popolate),
                'livello': livello,
                'aree_figlie': []
            }
            return area_data

        def processa_area_ricorsivamente(area, livello=0):
            """Processa un'area e tutte le sue aree figlie ricorsivamente mantenendo la gerarchia"""
            area_data = processa_area_singola(area, livello)

            # Processa le aree figlie dirette (non tutti i discendenti)
            # Usa l'ordine di default delle aree (non ordinare per nome)
            aree_figlie_dirette = area.get_children()

            # Aggiungi SEMPRE tutte le aree figlie, anche se non hanno case o religiosi
            for area_figlia in aree_figlie_dirette:
                area_figlia_data = processa_area_ricorsivamente(area_figlia, livello + 1)
                # Rimuovi il controllo if - aggiungi sempre l'area figlia
                area_data['aree_figlie'].append(area_figlia_data)

            return area_data

        def appiattisci_gerarchia(area_data):
            """Converte la struttura gerarchica in una lista piatta per il template"""
            risultato = []

            if isinstance(area_data, dict):
                # Aggiungi sempre l'area corrente (anche se non ha case o religiosi)
                area_per_template = {
                    'area_obj': area_data['area_obj'],
                    'elenco_case': area_data['elenco_case'],
                    'numero_case': area_data['numero_case'],
                    'livello': area_data['livello']
                }
                risultato.append(area_per_template)

                # Aggiungi ricorsivamente le aree figlie
                for area_figlia in area_data['aree_figlie']:
                    risultato.extend(appiattisci_gerarchia(area_figlia))

            return risultato

        # Processa l'area corrente dell'utente e le sue figlie
        if area_corrente:
            dati_gerarchici = processa_area_ricorsivamente(area_corrente, livello=0)
            elenco_aree_gerarchiche = appiattisci_gerarchia(dati_gerarchici)

        contesto = dict(
            elenco_province_case=elenco_aree_gerarchiche,
            intestazione_stampe=intestazione_stampe,
            data_corrente=timezone.now().date().strftime('%d/%m/%Y'),
        )
        filename = 'annuario_componenti_case_per_area_%s_%s.docx' % (
            slugify(request.user.area_corrente).upper(), slugify(timezone.now().date())
        )
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        doc = DocxTemplate(template)
        doc.render(contesto)
        doc.save(response)
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response
    stampa_annuario_componenti_case_per_area.short_description = _('Stampa Annuario Componenti Case per Area')

    def stampa_annuario_nazionalita(self, request):
        intestazione_stampe = preferences.ImpostazioniMagister.intestazione_stampe
        template = get_template_stampa('religiosi_annuario_nazionalita', request.user.language)
        if not exists(settings.WEBODT_TEMPLATE_PATH + '/' + template):
            template = get_template_stampa('religiosi_annuario_nazionalita')
        elenco_nazioni = Stato.objects.all()
        nazioni_con_religiosi = []
        totale_religiosi = 0
        voci_curriculum_postulanti_novizie = VoceCurriculum.objects.filter(
            Q(nome__icontains='postulant') | Q(nome__icontains='novizi')
        )
        for nazione in elenco_nazioni:
            elenco_religiosi_nazione = Religioso.objects.filter(
                nazione_nascita=nazione, data_morte__isnull=True, data_uscita__isnull=True
            ).exclude(
                status_religioso__in=voci_curriculum_postulanti_novizie
            ).order_by('appellativo', 'cognome', 'nome')
            if elenco_religiosi_nazione:
                nazione.elenco_religiosi = elenco_religiosi_nazione
                nazione.numero_religiosi = elenco_religiosi_nazione.count()
                totale_religiosi += elenco_religiosi_nazione.count()
                nazioni_con_religiosi.append(nazione)
        contesto = dict(
            elenco_nazioni=nazioni_con_religiosi,
            totale_religiosi=totale_religiosi,
            intestazione_stampe=intestazione_stampe,
            data_corrente=timezone.now().date().strftime('%d/%m/%Y'),
        )
        filename = 'annuario_nazionalita_%s_%s.docx' % (
            slugify(request.user.area_corrente).upper(), slugify(timezone.now().date())
        )
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        doc = DocxTemplate(template)
        doc.render(contesto)
        doc.save(response)
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response
    stampa_annuario_nazionalita.short_description = _('Stampa Annuario Nazionalità')

    def get_queryset(self, request):
        return super(ReligiosoAdmin, self).get_queryset(request).select_related(
            'casa_attuale', 'cittadinanza', 'nazione_nascita', 'nazionalita', 'provincia_religiosa',
            'regione_religiosa', 'provincia_residenza', 'nazione_residenza', 'nazione_morte',
        )

    def get_readonly_fields(self, request, obj=None):
        if settings.PUBLIUS_TAB_NASCOSTI:
            if self.suit_form_tabs:
                for tab in self.suit_form_tabs:
                    if tab[0] in settings.PUBLIUS_ELENCO_TAB_NASCOSTI:
                        self.suit_form_tabs.remove(tab)
        readonly_fields = super(ReligiosoAdmin, self).get_readonly_fields(request, obj)
        if not request.user.is_superuser:
            readonly_fields.append('utente')
        if not request.user.has_perm('persone.change_note_riservate'):
            if request.user.has_perm('persone.change_religioso'):
                readonly_fields.append('note_riservate')
        else:
            if request.user.has_perm('persone.view_religioso'):
                pass
        return readonly_fields

    def get_actions(self, request):
        actions = super().get_actions(request)
        if not request.user.is_superuser:
            if 'crea_utenti_ego' in actions:
                actions.pop('crea_utenti_ego')
        return actions

    def crea_utenti_ego(self, request, queryset):
        for religioso in queryset:
            messaggio = religioso.crea_utente()
            if messaggio.startswith('ATTENZIONE'):
                messages.warning(request, messaggio)
            elif messaggio.startswith('ERRORE'):
                messages.error(request, messaggio)
            else:
                messages.success(request, messaggio)
    crea_utenti_ego.short_description = _('Crea Utenti EGO')


site.register(Religioso, ReligiosoAdmin)


class PersoneEconomatoAdmin(ReligiosoAdmin):
    inlines = [RateoPensioneInline, ]
    suit_form_tabs = [
        ('dati_principali', _('Dati Principali')),
        ('nascita', _('Nascita')),
        ('residenza', _('Residenza')),
        ('notizie', _('Notizie')),
        ('salute', _('Salute')),
        ('uscita', _('Uscita')),
        ('decesso', _('Decesso')),
        ('ratei_pensioni', _('Ratei Pensioni')),
    ]
    actions = (
        'stampa_elenco_semplice', 'stampa_elenco_indirizzi',
    )

site.register(PersoneEconomato, PersoneEconomatoAdmin)


class TipoServizioAdmin(ImportMixin, BaseTipoAdmin):
    resource_class = TipoServizioResource

site.register(TipoServizio, TipoServizioAdmin)


class TipoStudioAdmin(ImportMixin, BaseTipoAdmin):
    resource_class = TipoStudioResource

site.register(TipoStudio, TipoStudioAdmin)


class TipoDomicilioAdmin(ImportMixin, BaseTipoAdmin):
    resource_class = TipoDomicilioResource

site.register(TipoDomicilio, TipoDomicilioAdmin)


site.register(Status, BaseTipoAdmin)
site.register(GruppoAppartenenza, BaseTipoAdmin)
site.register(GradoParentela, BaseTipoAdmin)
site.register(TipoDocumento, BaseTipoAdmin)
site.register(TipoPensione, BaseTipoAdmin)
site.register(ModalitaIncasso, BaseTipoAdmin)
site.register(RegioneReligiosa, BaseTipoAdmin)
site.register(Lingua, BaseTipoAdmin)
site.register(PosizioneCanonica, BaseTipoAdmin)
site.register(Incarico, BaseTipoAdmin)
