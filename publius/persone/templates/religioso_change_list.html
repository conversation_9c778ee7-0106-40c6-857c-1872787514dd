{% extends "admin/change_list.html" %}
{% load  i18n %}

{% block extrastyle %}
{{ block.super }}
<style type="text/css">
  .totale_ok {text-align:right; padding:0.3em 0.5em;}
  .totale_ko {text-align:right; background-color:red; color:black; padding:0.3em 0.5em;}
</style>
{% endblock %}

{% block object-tools %}
<ul class="object-tools">
  {# <li><a href="/publius/persone/religioso/import/">{% blocktrans %}Import{% endblocktrans %}</a></li> #}
  {% comment %} {% if perms.persone.view_stampe_annuario %}
  <li><a href="{% url 'publius:persone_religioso_stampaannuariofamiliari' %}">{% blocktrans %}Ann. Familiari{% endblocktrans %}</a></li>
  <li><a href="{% url 'publius:persone_religioso_stampaannuariodefunti' %}">{% blocktrans %}Ann. Defunti{% endblocktrans %}</a></li>
  <li><a href="{% url 'publius:persone_religioso_stampaannuariocase' %}">{% blocktrans %}Ann. Case{% endblocktrans %}</a></li>
  <li><a href="{% url 'publius:persone_religioso_stampaannuariocaseperarea' %}">{% blocktrans %}Ann. Case per Area{% endblocktrans %}</a></li>
  <li><a href="{% url 'publius:persone_religioso_stampaannuarionazionalita' %}">{% blocktrans %}Ann. Nazionalita{% endblocktrans %}</a></li>
  {% endif %} {% endcomment %}
  {% if has_add_permission %}
    <li><a href="add/{% if is_popup %}?_popup=1{% endif %}" class="addlink">{% blocktrans with cl.opts.verbose_name|escape as name %}Aggiungi{% endblocktrans %}</a></li>
  {% endif %}
</ul>
{% endblock %}
